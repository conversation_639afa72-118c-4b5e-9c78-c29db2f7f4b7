import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import type { RECHTE_RESPONSE_DTO } from '@/lib/backend/rechte/DTOs'
import { useFilterStore } from '@/lib/stores/useFilterStore'
import { useRechteFilter } from '@/lib/stores/useRechteFilter'
import { ChevronRightIcon, LockIcon, UserIcon, UsersIcon } from 'lucide-react'
import { motion } from 'motion/react'
import { GROUP_NAMES } from './constants'
import { TreeView } from './tree-view'
import { useData } from './useData'

export const RechteTreeView = ({ rechte }: { rechte: RECHTE_RESPONSE_DTO }) => {
  const { primaryPermissions, userGroupRechte } = useData()

  const { expandedGroups, toggleGroupExpansion } = useFilterStore()

  const { filteredRechte } = useRechteFilter(
    rechte,
    primaryPermissions,
    userGroupRechte,
  )

  if (!primaryPermissions) {
    return (
      <div className="space-y-6 p-8 rounded-2xl bg-background/50 backdrop-blur-sm border border-border/50 overflow-y-scroll max-h-[calc(100vh-200px)]">
        <h1 className="text-2xl font-semibold text-foreground tracking-tight">
          Berechtigungen
        </h1>
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <LockIcon className="w-16 h-16 text-muted-foreground/50 mb-4" />
          <h3 className="text-lg font-medium text-muted-foreground mb-2">
            Kein Benutzer ausgewählt
          </h3>
          <p className="text-muted-foreground/60 max-w-md">
            Wählen Sie einen Benutzer aus der Seitenleiste aus, um dessen
            Berechtigungen anzuzeigen.
          </p>
        </div>
      </div>
    )
  }

  const UNIQUE_OBJECT_GROUPS = filteredRechte
    .map((recht) => recht.objectGroup)
    .filter((group): group is string => group !== undefined)
    .filter((group, index, array) => array.indexOf(group) === index)

  if (UNIQUE_OBJECT_GROUPS.length === 0) {
    return (
      <div className="space-y-6 p-8 rounded-2xl bg-background/50 backdrop-blur-sm border border-border/50 overflow-y-scroll max-h-[calc(100vh-200px)]">
        <h1 className="text-2xl font-semibold text-foreground tracking-tight">
          Berechtigungen
        </h1>
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <LockIcon className="w-16 h-16 text-muted-foreground/50 mb-4" />
          <h3 className="text-lg font-medium text-muted-foreground mb-2">
            Keine Berechtigungen verfügbar
          </h3>
        </div>
      </div>
    )
  }

  const uniqueGroupNames = [
    ...new Set(userGroupRechte.map((r) => r.grpName).filter(Boolean)),
  ]

  const hasExpandedGroups = expandedGroups.size > 0
  const allGroupsExpanded = UNIQUE_OBJECT_GROUPS.every((group) =>
    expandedGroups.has(group),
  )

  return (
    <div className="space-y-6 p-8 rounded-2xl bg-background/50 backdrop-blur-sm border border-border/50 overflow-y-scroll max-h-[calc(100vh-200px)]">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-foreground tracking-tight">
          Berechtigungen
        </h1>
        <div className="flex items-center gap-3">
          {primaryPermissions.length > 0 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, ease: 'easeInOut' }}
              className="flex items-center gap-2 text-sm text-muted-foreground bg-primary/10 px-3 py-1 rounded-full border border-primary/20"
            >
              <UserIcon className="w-4 h-4" />
              <span className="text-primary-gradient">
                {primaryPermissions.length} direkte Rechte
              </span>
            </motion.div>
          )}
          {userGroupRechte.length > 0 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, ease: 'easeInOut' }}
              className="flex items-center gap-2 text-sm text-muted-foreground bg-secondary/10 px-3 py-1 rounded-full border border-secondary/20"
            >
              <UsersIcon className="w-4 h-4" />
              <span className="text-secondary-gradient">
                {userGroupRechte.length} Gruppenrechte
              </span>
              {uniqueGroupNames.length > 0 && (
                <span className="text-xs text-muted-foreground/60">
                  ({uniqueGroupNames.length} Gruppen)
                </span>
              )}
            </motion.div>
          )}
        </div>
      </div>

      <div className="space-y-3">
        {UNIQUE_OBJECT_GROUPS.map((group) => {
          const isExpanded = expandedGroups.has(group)
          const groupRechte = filteredRechte.filter(
            (permission) => permission.objectGroup === group,
          )

          return (
            <Collapsible
              key={group}
              open={isExpanded}
              onOpenChange={() => toggleGroupExpansion(group)}
              className="space-y-3"
            >
              <CollapsibleTrigger className="flex items-center w-full justify-between group data-[state=open]:border-primary/50 data-[state=open]:shadow-lg data-[state=open]:bg-primary/5 transition-all duration-200 rounded-xl p-4 border border-border/50 hover:border-primary/30 hover:bg-accent/50">
                <div className="flex items-center gap-3">
                  <ChevronRightIcon
                    className={`w-5 h-5 transition-transform duration-200 ${isExpanded ? 'rotate-90' : ''}`}
                  />
                  <h2 className="text-lg font-medium text-foreground group-hover:text-primary transition-colors duration-200">
                    {GROUP_NAMES[group] || 'Allgemein'}
                  </h2>
                </div>
                <span className="px-2 py-1 rounded-full text-xs font-medium bg-muted text-muted-foreground">
                  {groupRechte.length}
                </span>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-2 pl-8">
                <TreeView
                  all_permissions={groupRechte}
                  user_permissions={primaryPermissions}
                  group_permissions={userGroupRechte}
                  onPermissionChange={() => {}}
                />
              </CollapsibleContent>
            </Collapsible>
          )
        })}
      </div>
    </div>
  )
}
