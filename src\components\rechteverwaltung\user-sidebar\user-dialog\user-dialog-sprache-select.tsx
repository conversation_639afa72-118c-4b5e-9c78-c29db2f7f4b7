import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import React from 'react'

export const UserDialogSpracheSelect = ({
  defaultValue,
}: {
  defaultValue: string | null | undefined
}) => {
  const value = defaultValue ? defaultValue.toLowerCase() : 'de'

  return (
    <React.Fragment>
      <Select value={value}>
        <SelectTrigger>
          <SelectValue placeholder="Sprache" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="de">Deutsch</SelectItem>
          <SelectItem value="en">Englisch</SelectItem>
        </SelectContent>
      </Select>
    </React.Fragment>
  )
}
