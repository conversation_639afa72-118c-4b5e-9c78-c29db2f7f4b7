import { toast } from 'sonner'
import { fetchClient } from '../client'
import type { CREATE_GROUP_REQUEST_DTO } from './DTOs'

export async function createGroup(newGroup: CREATE_GROUP_REQUEST_DTO) {
  const response = await fetchClient.POST('/api/groups', {
    body: newGroup,
  })

  if (response.error) {
    throw new Error('Fehler beim Erstellen der Gruppe')
  }

  toast('Neue Gruppe erstellt.')
  return response.data
}
