import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import type { CREATE_USER_REQUEST_DTO } from '@/lib/backend/user/DTOs'
import { checkUserExists } from '@/lib/backend/user/functions'
import { useCreateUser } from '@/lib/backend/user/hooks'
import {
  AtSign,
  Badge,
  Building2,
  Hash,
  KeyRound,
  Mail,
  Save,
  ShieldCheck,
  User2,
  UserPlus,
} from 'lucide-react'
import { useState } from 'react'
import { toast } from 'sonner'
import { UserDialogFirmaSelect } from '../user-dialog/user-dialog-firma-select'
import { UserDialogCreateUserPW } from './add-dialog-create-user-pw'

export const UserDialogCreateUserContent = ({}) => {
  const [newUser, setNewUser] = useState<CREATE_USER_REQUEST_DTO>({
    id: '',
    name: '',
    password: '',
    shortName: '',
    numberId: '',
    title: '',
    email: '',
    companyRef: 0,
    isAdmin: false,
  })
  const [isOpen, setIsOpen] = useState(false)

  const createUserMutation = useCreateUser()

  const handleInputChange = (
    field: keyof CREATE_USER_REQUEST_DTO,
    value: string | number | boolean,
  ) => {
    setNewUser((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleCreateUserSubmit = async () => {
    try {
      await createUserMutation.mutateAsync(newUser)
      setIsOpen(false)
      setNewUser({
        id: '',
        name: '',
        password: '',
        shortName: '',
        numberId: '',
        title: '',
        email: '',
        companyRef: 0,
        isAdmin: false,
      })
      toast.success('Neuer Benutzer erstellt.')
    } catch (error) {
      console.error('Fehler beim Erstellen des Benutzers:', error)
      toast.error('Fehler beim Erstellen des Benutzers')
    }
  }

  const handleSubmit = async () => {
    try {
      const userExistsData = await checkUserExists(newUser.id)
      if (userExistsData) {
        toast('Benutzer-ID existiert bereits. Bitte eine andere ID verwenden.')
        return
      }
      setIsOpen(true)
    } catch (error) {
      console.error('Fehler beim Erstellen des Benutzers:', error)
    }
  }

  const isLoading = createUserMutation.isPending

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-8">
      <div className="relative">
        <div className="flex items-center gap-6 pb-6 border-b border-border/50">
          <div className="w-16 h-16 rounded-2xl bg-primary-gradient flex items-center justify-center ring-1 ring-border/20">
            <UserPlus className="w-8 h-8 text-primary" />
          </div>
          <div className="flex-1 min-w-0">
            <h1 className="text-2xl font-semibold text-foreground tracking-tight mb-2">
              Neuen Benutzer erstellen
            </h1>
            <p className="text-muted-foreground">
              Fülle alle erforderlichen Felder aus, um einen neuen Benutzer zu
              erstellen.
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          <div>
            <div className="flex items-center gap-2 mb-6">
              <Badge className="w-5 h-5 text-primary" />
              <h2 className="text-lg font-medium text-foreground">
                Benutzer-Details
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label
                  htmlFor="name"
                  className="text-sm font-medium text-muted-foreground flex items-center gap-2"
                >
                  <User2 className="w-4 h-4" />
                  Name (Bezeichnung)
                </Label>
                <Input
                  id="name"
                  type="text"
                  value={newUser.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Vollständiger Name"
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="email"
                  className="text-sm font-medium text-muted-foreground flex items-center gap-2"
                >
                  <Mail className="w-4 h-4" />
                  E-Mail
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={newUser.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="shortName"
                  className="text-sm font-medium text-muted-foreground flex items-center gap-2"
                >
                  <AtSign className="w-4 h-4" />
                  Kürzel
                </Label>
                <Input
                  id="shortName"
                  type="text"
                  value={newUser.shortName}
                  onChange={(e) =>
                    handleInputChange('shortName', e.target.value)
                  }
                  placeholder="max.m"
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="userId"
                  className="text-sm font-medium text-muted-foreground flex items-center gap-2"
                >
                  <KeyRound className="w-4 h-4" />
                  Benutzer-ID
                </Label>
                <Input
                  id="userId"
                  type="text"
                  value={newUser.id}
                  onChange={(e) => handleInputChange('id', e.target.value)}
                  placeholder="USER_001"
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="title"
                  className="text-sm font-medium text-muted-foreground flex items-center gap-2"
                >
                  <Badge className="w-4 h-4" />
                  Funktion
                </Label>
                <Input
                  id="title"
                  type="text"
                  value={newUser.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Funktion"
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="numberId"
                  className="text-sm font-medium text-muted-foreground flex items-center gap-2"
                >
                  <Hash className="w-4 h-4" />
                  Nummer-ID
                </Label>
                <Input
                  id="numberId"
                  type="text"
                  value={newUser.numberId}
                  onChange={(e) =>
                    handleInputChange('numberId', e.target.value)
                  }
                  placeholder="12345"
                  disabled={isLoading}
                />
              </div>
            </div>
          </div>

          <div className="pt-6 border-t border-border/30">
            <div className="flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-primary/5 to-primary/10 border border-primary/10">
              <Checkbox
                checked={newUser.isAdmin}
                onCheckedChange={(checked) =>
                  handleInputChange('isAdmin', !!checked)
                }
                id="admin-checkbox"
                className="flex-shrink-0"
                disabled={isLoading}
              />
              <Label
                htmlFor="admin-checkbox"
                className="cursor-pointer flex items-center gap-3 flex-1"
              >
                <ShieldCheck className="w-5 h-5 text-primary" />
                <div>
                  <span className="text-base font-medium text-foreground block">
                    Administrator-Berechtigung
                  </span>
                  <span className="text-sm text-muted-foreground">
                    Erteilt dem Benutzer erweiterte Systemrechte
                  </span>
                </div>
              </Label>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <div className="flex items-center gap-2 mb-6">
              <Building2 className="w-5 h-5 text-primary" />
              <h2 className="text-lg font-medium text-foreground">Zuordnung</h2>
            </div>

            <div className="space-y-5">
              <div>
                <Label className="text-sm font-medium text-muted-foreground flex items-center gap-2 mb-3">
                  <Building2 className="w-4 h-4" />
                  Firma
                </Label>
                <UserDialogFirmaSelect
                  defaultValue={newUser.companyRef || undefined}
                  onChange={(value: string) =>
                    handleInputChange('companyRef', parseInt(value))
                  }
                />
              </div>
            </div>
          </div>

          <div className="pt-6 border-t border-border/30">
            <Button
              onClick={handleSubmit}
              className="w-full gap-2"
              size="lg"
              disabled={isLoading}
            >
              <Save className="w-4 h-4" />
              {isLoading ? 'Erstelle Benutzer...' : 'Benutzer erstellen'}
            </Button>
          </div>
        </div>
      </div>
      {isOpen && (
        <UserDialogCreateUserPW
          newUser={newUser}
          setNewUser={setNewUser}
          handleCreateUserSubmit={handleCreateUserSubmit}
          setIsOpen={setIsOpen}
        />
      )}
    </div>
  )
}
