import { Combobox } from '@/components/ui/combobox'
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import type { UserItem } from '@/lib/backend/user/DTOs'
import { useUsers } from '@/lib/backend/user/hooks'
import { useState } from 'react'
import { UserDialogGroupChangeContent } from './user-dialog-group-change-content'
import { UserDialogUserCompare } from './user-dialog-user-compare/user-dialog-user-compare'
import { UserDialogUserContent } from './user-dialog-user-content'

export type UserDialogMode =
  | 'details'
  | 'assign-group'
  | 'group'
  | 'user-compare'

interface UserDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  mode: UserDialogMode
  user: UserItem | null
}

export const UserDialog = ({
  open,
  onOpenChange,
  mode,
  user,
}: UserDialogProps) => {
  const [selectedCompareUser, setSelectedCompareUser] =
    useState<UserItem | null>(null)
  const { data: users } = useUsers()
  if (!user || !user.ref) return null

  const UserComboboxItems =
    users?.map((u) => ({
      value: u.ref?.toString() || '',
      label: u.userName || 'Unbekannt',
    })) || []

  const getDialogTitle = () => {
    switch (mode) {
      case 'details':
        return 'Eigenschaften'
      case 'assign-group':
        return 'Gruppe zuweisen'
      case 'user-compare':
        return (
          <div className="flex items-center justify-between pr-12 gap-2 w-full">
            <h1>Benutzer verlgeichen</h1>
            <Combobox
              items={UserComboboxItems}
              placeholder="Wähle eine Benutzer"
              multipleSelection
              value={
                selectedCompareUser ? [selectedCompareUser.ref!.toString()] : []
              }
              setValue={(refs) => {
                const selected = users?.find(
                  (u) => u.ref?.toString() === refs[0],
                )
                setSelectedCompareUser(selected || null)
              }}
            />
          </div>
        )
      default:
        return 'Benutzer'
    }
  }

  const renderContent = () => {
    switch (mode) {
      case 'assign-group':
        return <UserDialogGroupChangeContent userRef={user.ref!} />
      case 'details':
        return (
          <UserDialogUserContent
            user={user}
            onClose={() => onOpenChange(false)}
          />
        )
      case 'user-compare':
        return (
          <UserDialogUserCompare
            user={user}
            selectedCompareUser={selectedCompareUser}
            setSelectedCompareUser={setSelectedCompareUser}
          />
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="min-w-4xl">
        <DialogHeader>
          <DialogTitle>{getDialogTitle()}</DialogTitle>
        </DialogHeader>
        {renderContent()}
      </DialogContent>
    </Dialog>
  )
}
