import { useSuspenseQuery } from '@tanstack/react-query'
import {
  rechtegroupQueryOptions,
  rechteQueryOptions,
  rechteuserQueryOptions,
} from './queries.ts'

export const useRechte = () => {
  return useSuspenseQuery(rechteQueryOptions)
}

export const useRechteUser = (ACO_REF: number) => {
  return useSuspenseQuery(rechteuserQueryOptions(ACO_REF))
}

export const useRechteGroup = (ACO_REF: number) => {
  return useSuspenseQuery(rechtegroupQueryOptions(ACO_REF))
}
