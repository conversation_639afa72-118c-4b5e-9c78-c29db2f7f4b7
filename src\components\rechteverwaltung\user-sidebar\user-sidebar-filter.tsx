import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Filter, Search } from 'lucide-react'

type FilterType = 'all' | 'users' | 'groups'

interface UserSidebarFilterProps {
  searchTerm: string
  onSearchChange: (value: string) => void
  filterType: FilterType
  onFilterTypeChange: (type: FilterType) => void
  showActiveOnly: boolean
  onShowActiveOnlyChange: (checked: boolean) => void
}

export const UserSidebarFilter = ({
  searchTerm,
  onSearchChange,
  filterType,
  onFilterTypeChange,
  showActiveOnly,
  onShowActiveOnlyChange,
}: UserSidebarFilterProps) => {
  return (
    <div className="flex flex-col gap-4 py-2">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder="Benutzer durchsuchen..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10 h-9 border-gray-200 focus:border-[#57DD00] focus:ring-2 focus:ring-[#57DD00]/20 transition-all"
        />
      </div>

      <div className="flex flex-col gap-3">
        <div className="flex justify-between gap-2">
          <div className='flex items-center gap-2'>
            <Filter className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium">Filter:</span>
          </div>
          <div className="flex items-center gap-2">
            <Checkbox
              id="activesCheckBox"
              checked={showActiveOnly}
              onCheckedChange={(checked) =>
                onShowActiveOnlyChange(checked === true)
              }
            />
            <Label htmlFor="activesCheckBox" className="text-sm cursor-pointer">
              Nur aktive
            </Label>
          </div>
        </div>

        <div className="flex gap-2">
          {(['all', 'users', 'groups'] as const).map((type) => (
            <Button
              key={type}
              variant={filterType === type ? 'default' : 'outline'}
              size="sm"
              onClick={() => onFilterTypeChange(type)}
              className="h-8 text-xs"
            >
              {type === 'all' && 'Alle'}
              {type === 'users' && 'Nur Nutzer'}
              {type === 'groups' && 'Nur Gruppen'}
            </Button>
          ))}
        </div>

        <Separator className="my-1" />
      </div>
    </div>
  )
}
