@font-face {
  font-family: 'RadialSoft';
  src: url('/font/RadialSoft-D-Regular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'RadialSoft';
  src: url('/font/RadialSoft-D-RegularItalic.otf') format('opentype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'RadialSoft';
  src: url('/font/RadialSoft-D-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@import 'tailwindcss';

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

body {
  @apply m-0;
  font-family:
    'RadialSoft',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    'Roboto',
    'Oxygen',
    'Ubuntu',
    'Can<PERSON>ell',
    '<PERSON><PERSON>',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family:
    source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: linear-gradient(
    180deg,
    oklch(0.7911 0.247 138.52),
    oklch(0.8621 0.1964 117.33)
  );
  --primary-foreground: oklch(0.21 0.006 285.885);
  --secondary: linear-gradient(
    180deg,
    oklch(0.594 0.2797 304.97),
    oklch(0.7016 0.2905 324.83)
  );
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: linear-gradient(
    180deg,
    oklch(0.7463 0.1582 59.67),
    oklch(0.881 0.181372 96.374)
  );
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.7911 0.247 138.52);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.21 0.006 285.885);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.871 0.006 286.286);
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.141 0.005 285.823);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.141 0.005 285.823);
  --popover-foreground: oklch(0.985 0 0);
  --primary: linear-gradient(
    180deg,
    oklch(0.7911 0.247 138.52),
    oklch(0.8621 0.1964 117.33)
  );
  --primary-foreground: oklch(0.985 0 0);
  --secondary: linear-gradient(
    180deg,
    oklch(0.594 0.2797 304.97),
    oklch(0.7016 0.2905 324.83)
  );
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: linear-gradient(
    180deg,
    oklch(0.7463 0.1582 59.67),
    oklch(0.881 0.181372 96.374)
  );
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.274 0.006 286.033);
  --input: oklch(0.274 0.006 286.033);
  --ring: oklch(0.442 0.017 285.786);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.274 0.006 286.033);
  --sidebar-ring: oklch(0.442 0.017 285.786);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .bg-primary-gradient {
    background: var(--primary);
  }

  .bg-secondary-gradient {
    background: var(--secondary);
  }

  .bg-accent-gradient {
    background: var(--accent);
  }

  .hover\:bg-primary-gradient-hover:hover {
    background: var(--primary);
    filter: brightness(0.9);
  }

  .hover\:bg-secondary-gradient-hover:hover {
    background: var(--secondary);
    filter: brightness(0.9);
  }

  .hover\:bg-accent-gradient-hover:hover {
    background: var(--accent);
    filter: brightness(0.9);
  }

  .text-primary-gradient {
    background: var(--primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .text-secondary-gradient {
    background: var(--secondary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .font-radial {
    font-family: 'RadialSoft', sans-serif;
  }

  .ring-primary\/20 {
    box-shadow: 0 0 0 2px oklch(0.7911 0.247 138.52 / 0.2);
  }

  .border-primary\/40 {
    border-color: oklch(0.7911 0.247 138.52 / 0.4);
  }

  .hover\:border-primary\/40:hover {
    border-color: oklch(0.7911 0.247 138.52 / 0.4);
  }

  .focus-visible\:border-primary-gradient:focus-visible {
    border: 2px solid transparent;
    background:
      linear-gradient(var(--background), var(--background)) padding-box,
      var(--primary) border-box;
  }

  .focus-visible\:ring-primary\/20:focus-visible {
    box-shadow: 0 0 0 2px oklch(0.7911 0.247 138.52 / 0.2);
  }

  .dark .ring-primary\/20 {
    box-shadow: 0 0 0 2px oklch(0.7911 0.247 138.52 / 0.3);
  }

  .dark .border-primary\/40 {
    border-color: oklch(0.7911 0.247 138.52 / 0.5);
  }

  .dark .hover\:border-primary\/40:hover {
    border-color: oklch(0.7911 0.247 138.52 / 0.5);
  }

  .dark .focus-visible\:ring-primary\/20:focus-visible {
    box-shadow: 0 0 0 2px oklch(0.7911 0.247 138.52 / 0.3);
  }
}
