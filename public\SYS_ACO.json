{"recordset": [{"REF": 467, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.PackplatzMainMenuItem", "OBJECT_ID": 172, "NAME": "PackplatzMainMenuItem", "TYP": "MENU", "BESCHREIBUNG": "Verpackungsplätze verwaltenasd", "OBJECT_GROUP": "PACK", "PARENT_REF": null, "LEVEL": 0, "SORT_ORDER": 1}, {"REF": 468, "APPLICATION": "PCD", "OBJECT_NAME": "PackForm.CreatePackplatzButton", "OBJECT_ID": 173, "NAME": "CreatePackplatzButton", "TYP": "BUTTON", "BESCHREIBUNG": "Neuen Verpackungsplatz erstellen", "OBJECT_GROUP": "PACK", "PARENT_REF": 467, "LEVEL": 1, "SORT_ORDER": 1}, {"REF": 469, "APPLICATION": "PCD", "OBJECT_NAME": "PackForm.CreateStandardPackplatzButton", "OBJECT_ID": 174, "NAME": "CreateStandardPackplatzButton", "TYP": "BUTTON", "BESCHREIBUNG": "Standard Verpackungsplatz erstellen", "OBJECT_GROUP": "PACK", "PARENT_REF": 468, "LEVEL": 2, "SORT_ORDER": 1}, {"REF": 470, "APPLICATION": "PCD", "OBJECT_NAME": "PackForm.CreateExpressPackplatzButton", "OBJECT_ID": 175, "NAME": "CreateExpressPackplatzButton", "TYP": "BUTTON", "BESCHREIBUNG": "Express Verpackungsplatz für eilige Aufträge", "OBJECT_GROUP": "PACK", "PARENT_REF": 468, "LEVEL": 2, "SORT_ORDER": 2}, {"REF": 471, "APPLICATION": "PCD", "OBJECT_NAME": "PackForm.EditPackplatzContextMenu", "OBJECT_ID": 176, "NAME": "EditPackplatzContextMenu", "TYP": "CONTEXTMENU", "BESCHREIBUNG": "Verpackungsplatz bearbeiten", "OBJECT_GROUP": "PACK", "PARENT_REF": 467, "LEVEL": 1, "SORT_ORDER": 2}, {"REF": 472, "APPLICATION": "PCD", "OBJECT_NAME": "PackForm.EditConfigurationButton", "OBJECT_ID": 177, "NAME": "EditConfigurationButton", "TYP": "BUTTON", "BESCHREIBUNG": "Packplatz-Konfiguration bearbeiten", "OBJECT_GROUP": "PACK", "PARENT_REF": 471, "LEVEL": 2, "SORT_ORDER": 1}, {"REF": 473, "APPLICATION": "PCD", "OBJECT_NAME": "PackForm.AdjustCapacityButton", "OBJECT_ID": 178, "NAME": "AdjustCapacityButton", "TYP": "BUTTON", "BESCHREIBUNG": "Packplatz-Kapazität anpassen", "OBJECT_GROUP": "PACK", "PARENT_REF": 471, "LEVEL": 2, "SORT_ORDER": 2}, {"REF": 474, "APPLICATION": "PCD", "OBJECT_NAME": "PackForm.AssignOrdersButton", "OBJECT_ID": 179, "NAME": "Assign<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TYP": "BUTTON", "BESCHREIBUNG": "Aufträge zu Packplätzen zu<PERSON>sen", "OBJECT_GROUP": "PACK", "PARENT_REF": 467, "LEVEL": 1, "SORT_ORDER": 3}, {"REF": 475, "APPLICATION": "PCD", "OBJECT_NAME": "PackForm.PackingMaterialMenu", "OBJECT_ID": 180, "NAME": "PackingMaterialMenu", "TYP": "MENU", "BESCHREIBUNG": "Verpackungsmaterial verwalten", "OBJECT_GROUP": "PACK", "PARENT_REF": 467, "LEVEL": 1, "SORT_ORDER": 4}, {"REF": 476, "APPLICATION": "PCD", "OBJECT_NAME": "PackForm.SelectMaterialButton", "OBJECT_ID": 181, "NAME": "SelectMaterialButton", "TYP": "BUTTON", "BESCHREIBUNG": "Verpackungsmaterial auswählen", "OBJECT_GROUP": "PACK", "PARENT_REF": 475, "LEVEL": 2, "SORT_ORDER": 1}, {"REF": 478, "APPLICATION": "PCD", "OBJECT_NAME": "SpeditionsAuftrag.ChangeArtikel", "OBJECT_ID": 183, "NAME": "ChangeArtikel", "TYP": "ACO", "BESCHREIBUNG": "Artikel ändern", "OBJECT_GROUP": "ARTIKEL", "PARENT_REF": null, "LEVEL": 0, "SORT_ORDER": 1}, {"REF": 479, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.AufImportMenuItem", "OBJECT_ID": 184, "NAME": "AufImportMenuItem", "TYP": "MENU", "BESCHREIBUNG": "Auftragsdaten importieren", "OBJECT_GROUP": "IMPORT", "PARENT_REF": null, "LEVEL": 0, "SORT_ORDER": 1}, {"REF": 480, "APPLICATION": "PCD", "OBJECT_NAME": "ImportForm.ValidateDataButton", "OBJECT_ID": 185, "NAME": "ValidateDataButton", "TYP": "BUTTON", "BESCHREIBUNG": "Importdaten validieren", "OBJECT_GROUP": "IMPORT", "PARENT_REF": 479, "LEVEL": 1, "SORT_ORDER": 1}, {"REF": 481, "APPLICATION": "PCD", "OBJECT_NAME": "ImportForm.ProcessDataButton", "OBJECT_ID": 186, "NAME": "ProcessDataButton", "TYP": "BUTTON", "BESCHREIBUNG": "Importdaten verarbeiten", "OBJECT_GROUP": "IMPORT", "PARENT_REF": 479, "LEVEL": 1, "SORT_ORDER": 2}, {"REF": 482, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.WEPosPrintEAN128MenuItem", "OBJECT_ID": 187, "NAME": "WEPosPrintEAN128MenuItem", "TYP": "MENU", "BESCHREIBUNG": "EAN128-<PERSON><PERSON><PERSON><PERSON> d<PERSON>cken", "OBJECT_GROUP": "WE", "PARENT_REF": null, "LEVEL": 0, "SORT_ORDER": 1}, {"REF": 483, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.WEPosPrintEAN13MenuItem", "OBJECT_ID": 188, "NAME": "WEPosPrintEAN13MenuItem", "TYP": "MENU", "BESCHREIBUNG": "EAN13-<PERSON><PERSON><PERSON><PERSON> d<PERSON>cken", "OBJECT_GROUP": "WE", "PARENT_REF": null, "LEVEL": 0, "SORT_ORDER": 2}, {"REF": 484, "APPLICATION": "PCD", "OBJECT_NAME": "ArtikelForm.Menue.PrintEAN128MenuItem", "OBJECT_ID": 189, "NAME": "PrintEAN128MenuItem", "TYP": "MENU", "BESCHREIBUNG": "ArtikelStamm:EAN128 drucken...", "OBJECT_GROUP": "ARTIKEL", "PARENT_REF": null, "LEVEL": 0, "SORT_ORDER": 1}, {"REF": 485, "APPLICATION": "PCD", "OBJECT_NAME": "ArtikelForm.PrintEAN128Button", "OBJECT_ID": 190, "NAME": "PrintEAN128Button", "TYP": "BUTTON", "BESCHREIBUNG": "ArtikelStamm:EAN128 drucken...", "OBJECT_GROUP": "ARTIKEL", "PARENT_REF": 484, "LEVEL": 1, "SORT_ORDER": 1}, {"REF": 486, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.WEDeleteMenuItem", "OBJECT_ID": 191, "NAME": "WEDeleteMenuItem", "TYP": "MENU", "BESCHREIBUNG": "Wareneingang löschen", "OBJECT_GROUP": "WE", "PARENT_REF": null, "LEVEL": 0, "SORT_ORDER": 3}, {"REF": 487, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.DruckenMainMenuItem", "OBJECT_ID": 192, "NAME": "DruckenMainMenuItem", "TYP": "MENU", "BESCHREIBUNG": "<PERSON><PERSON><PERSON>", "OBJECT_GROUP": "DRUCKEN", "PARENT_REF": null, "LEVEL": 0, "SORT_ORDER": 1}, {"REF": 488, "APPLICATION": "PCD", "OBJECT_NAME": "DruckForm.PrintLabelButton", "OBJECT_ID": 193, "NAME": "PrintLabelButton", "TYP": "BUTTON", "BESCHREIBUNG": "Etiket<PERSON> drucken", "OBJECT_GROUP": "DRUCKEN", "PARENT_REF": 487, "LEVEL": 1, "SORT_ORDER": 1}, {"REF": 181, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.LagerBesTabSheet", "OBJECT_ID": 29, "NAME": "LagerBesTabSheet", "TYP": "TAB", "BESCHREIBUNG": "Bestand", "OBJECT_GROUP": "BESTAND", "PARENT_REF": null, "LEVEL": 0, "SORT_ORDER": 1}, {"REF": 775, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.ChangeLagerBesMenuItem", "OBJECT_ID": 326, "NAME": "ChangeLagerBesMenuItem", "TYP": "MENU", "BESCHREIBUNG": "Bestand ändern...", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 775111, "LEVEL": 2, "SORT_ORDER": 1}, {"REF": 1354, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.LagerBesChangeAEMenuItem", "OBJECT_ID": 349, "NAME": "LagerBesChangeAEMenuItem", "TYP": "MENU", "BESCHREIBUNG": "Artikel ändern...", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 775111, "LEVEL": 2, "SORT_ORDER": 1}, {"REF": 3188, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.LagerBesChangeMHDChargeMenuItem", "OBJECT_ID": 410, "NAME": "LagerBesChangeMHDChargeMenuItem", "TYP": "MENU", "BESCHREIBUNG": "MHD / Charge bearbeiten", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 775111, "LEVEL": 2, "SORT_ORDER": 2}, {"REF": 3205, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.LagerBesChangeCategoryMenuItem", "OBJECT_ID": 413, "NAME": "LagerBesChangeCategoryMenuItem", "TYP": "MENU", "BESCHREIBUNG": "Bestandskategorie ändern", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 775111, "LEVEL": 2, "SORT_ORDER": 3}, {"REF": 253, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.AddBesButton", "OBJECT_ID": 65, "NAME": "AddBesButton", "TYP": "BUTTON", "BESCHREIBUNG": "<PERSON><PERSON> anlegen", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 181, "LEVEL": 1, "SORT_ORDER": 2}, {"REF": 687, "APPLICATION": "PCD", "OBJECT_NAME": "INVANG", "OBJECT_ID": 282, "NAME": "INVANG", "TYP": "ACO", "BESCHREIBUNG": "Bestände anlegen", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 253, "LEVEL": 2, "SORT_ORDER": 1}, {"REF": 255, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.DelBesButton", "OBJECT_ID": 66, "NAME": "DelBesButton", "TYP": "BUTTON", "BESCHREIBUNG": "Bestand löschen", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 181, "LEVEL": 1, "SORT_ORDER": 3}, {"REF": 275, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.ChangeBesButton2", "OBJECT_ID": 76, "NAME": "ChangeBesButton2", "TYP": "BUTTON", "BESCHREIBUNG": "Bestand ändern...", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 181, "LEVEL": 1, "SORT_ORDER": 4}, {"REF": 261, "APPLICATION": "PCD", "OBJECT_NAME": "ChangeBestand", "OBJECT_ID": 69, "NAME": "ChangeBestand", "TYP": "ACO", "BESCHREIBUNG": "Bestand ändern", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 275, "LEVEL": 2, "SORT_ORDER": 1}, {"REF": 477, "APPLICATION": "PCD", "OBJECT_NAME": "SpeditionsAuftrag.ChangeBestand", "OBJECT_ID": 182, "NAME": "ChangeBestand", "TYP": "ACO", "BESCHREIBUNG": "Bestand ändern", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 275, "LEVEL": 2, "SORT_ORDER": 2}, {"REF": 313, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.SperrBesButton2", "OBJECT_ID": 95, "NAME": "SperrBesButton2", "TYP": "BUTTON", "BESCHREIBUNG": "<PERSON><PERSON> sperren", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 181, "LEVEL": 1, "SORT_ORDER": 5}, {"REF": 315, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.FreeBesButton2", "OBJECT_ID": 96, "NAME": "FreeBesButton2", "TYP": "BUTTON", "BESCHREIBUNG": "Bestand freigeben", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 181, "LEVEL": 1, "SORT_ORDER": 6}, {"REF": 191, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.LagerResTabSheet", "OBJECT_ID": 34, "NAME": "LagerResTabSheet", "TYP": "TAB", "BESCHREIBUNG": "Bestandsreservierungen", "OBJECT_GROUP": "BESTAND", "PARENT_REF": null, "LEVEL": 0, "SORT_ORDER": 2}, {"REF": 3748, "APPLICATION": "PCD", "OBJECT_NAME": "ShowBestandResForm.Menue.DelBesResMenuItem", "OBJECT_ID": 436, "NAME": "DelBesResMenuItem", "TYP": "MENU", "BESCHREIBUNG": "Reservierung löschen", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 191, "LEVEL": 1, "SORT_ORDER": 1}, {"REF": 211, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.WarenBesTabSheet", "OBJECT_ID": 44, "NAME": "WarenBesTabSheet", "TYP": "TAB", "BESCHREIBUNG": "Warenbestand", "OBJECT_GROUP": "BESTAND", "PARENT_REF": null, "LEVEL": 0, "SORT_ORDER": 3}, {"REF": 641, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.WarenBesCategoryMenuItem", "OBJECT_ID": 259, "NAME": "WarenBesCategoryMenuItem", "TYP": "MENU", "BESCHREIBUNG": "Bestandsqualifikation", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 211, "LEVEL": 1, "SORT_ORDER": 1}, {"REF": 1302, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.WarenBesChargeAEMenuItem", "OBJECT_ID": 346, "NAME": "WarenBesChargeAEMenuItem", "TYP": "MENU", "BESCHREIBUNG": "Artikel eines Bestandes ändern", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 211, "LEVEL": 1, "SORT_ORDER": 2}, {"REF": 3186, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.WarenBesChargeMHDChargeMenuItem", "OBJECT_ID": 409, "NAME": "WarenBesChargeMHDChargeMenuItem", "TYP": "MENU", "BESCHREIBUNG": "MHD / Charge bearbeiten", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 211, "LEVEL": 1, "SORT_ORDER": 3}, {"REF": 257, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.AddBesButton2", "OBJECT_ID": 67, "NAME": "AddBesButton2", "TYP": "BUTTON", "BESCHREIBUNG": "Warenbestand anlegen", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 211, "LEVEL": 1, "SORT_ORDER": 4}, {"REF": 259, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.DelBesButton2", "OBJECT_ID": 68, "NAME": "DelBesButton2", "TYP": "BUTTON", "BESCHREIBUNG": "Warenbestand löschen", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 211, "LEVEL": 1, "SORT_ORDER": 5}, {"REF": 3201, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.LEBesChangeCategoryMenuItem", "OBJECT_ID": 411, "NAME": "LEBesChangeCategoryMenuItem", "TYP": "MENU", "BESCHREIBUNG": "Bestandskategorie ändern", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 777, "LEVEL": 1, "SORT_ORDER": 1}, {"REF": 3203, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.LEBesChangeMHDChargeMenuItem", "OBJECT_ID": 412, "NAME": "LEBesChangeMHDChargeMenuItem", "TYP": "MENU", "BESCHREIBUNG": "MHD / Charge bearbeiten", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 777, "LEVEL": 1, "SORT_ORDER": 2}, {"REF": 521, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.BesAufLPButton", "OBJECT_ID": 199, "NAME": "BesAufLPButton", "TYP": "BUTTON", "BESCHREIBUNG": "Auf LP buchen", "OBJECT_GROUP": "BESTAND", "PARENT_REF": 781, "LEVEL": 1, "SORT_ORDER": 1}, {"REF": 539, "APPLICATION": "PCD", "OBJECT_NAME": "MultiSelectBestand", "OBJECT_ID": 208, "NAME": "MultiSelectBestand", "TYP": "ACO", "BESCHREIBUNG": "MultiSelect beim <PERSON>and", "OBJECT_GROUP": "BESTAND", "PARENT_REF": null, "LEVEL": 0, "SORT_ORDER": 8}, {"REF": 3981, "APPLICATION": "PCD", "OBJECT_NAME": "ChangeStockSerialNr", "OBJECT_ID": 460, "NAME": "ChangeStockSerialNr", "TYP": "ACO", "BESCHREIBUNG": "Seriennummern ändern", "OBJECT_GROUP": "BESTAND", "PARENT_REF": null, "LEVEL": 0, "SORT_ORDER": 9}, {"REF": 775111, "APPLICATION": "PCD", "OBJECT_NAME": "LVSForm.Menue.ChangeLagerBesMenuItem", "OBJECT_ID": 326, "NAME": "ContextMenu Detailgrid", "TYP": "MENU", "BESCHREIBUNG": ".", "OBJECT_GROUP": "", "PARENT_REF": 181, "LEVEL": 1, "SORT_ORDER": 1}]}