import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import type { FirmaItem } from '@/lib/backend/firma/DTOs'
import { useFirmen } from '@/lib/backend/firma/hooks'
import React from 'react'

export const UserDialogFirmaSelect = ({
  defaultValue,
  onChange,
}: {
  defaultValue: number | null | undefined
  onChange?: (value: string) => void
}) => {
  const { data: firmen } = useFirmen()

  const typed_firmen = firmen as FirmaItem[]

  return (
    <React.Fragment>
      <Select value={defaultValue?.toString()} onValueChange={onChange}>
        <SelectTrigger>
          <SelectValue placeholder="Firma auswählen" />
        </SelectTrigger>
        <SelectContent>
          {typed_firmen.map((firma) => {
            if (!firma.ref) {
              return null
            }
            return (
              <SelectItem key={firma.ref} value={firma.ref?.toString()}>
                {firma.name}
              </SelectItem>
            )
          })}
        </SelectContent>
      </Select>
    </React.Fragment>
  )
}
