import StorelogixIcon from '@/components/storelogix-icons/sl-icons'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { containerVariants, itemVariants } from '@/lib/animations/variants'
import { Link, createFileRoute } from '@tanstack/react-router'
import { motion } from 'motion/react'
import { toast } from 'sonner'

export const Route = createFileRoute('/')({
  component: App,
})

function App() {
  http: return (
    <div className="min-h-screen0 p-8">
      <motion.div
        className="max-w-4xl mx-auto space-y-8"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div className="text-center space-y-4" variants={itemVariants}>
          <h1 className="text-4xl font-bold text-gray-900">
            Willkommen bei storelogix!
          </h1>
        </motion.div>
        <motion.div
          className="grid md:grid-cols-1 gap-6"
          variants={itemVariants}
        >
          <motion.div
            whileHover={{
              scale: 1.02,
              transition: { duration: 0.2 },
            }}
            whileTap={{ scale: 0.98 }}
          >
            <Card className="hover:shadow-lg transition-shadow h-full">
              <CardHeader>
                <CardTitle>Rechteverwaltung</CardTitle>
                <CardDescription>
                  Verwalte Benutzer und deren Berechtigungen
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link to="/rechteverwaltung">
                  <Button className="w-full">
                    <StorelogixIcon name="user" />
                    Zur Rechteverwaltung
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>

        <Button onClick={() => toast('Dies ist ein Beispieltoast!')}>
          asd
        </Button>
      </motion.div>
    </div>
  )
}
