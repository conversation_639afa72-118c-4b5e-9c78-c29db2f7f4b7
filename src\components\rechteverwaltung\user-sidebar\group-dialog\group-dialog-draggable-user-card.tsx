import { Checkbox } from '@/components/ui/checkbox'
import type { USER_RESPONSE_DTO } from '@/lib/backend/user/DTOs'
import { cn } from '@/lib/utils'
import { useDraggable, type UniqueIdentifier } from '@dnd-kit/core'

interface DraggableUserCardProps {
  user: USER_RESPONSE_DTO[number]
  isDragOverlay?: boolean
  isSelected: boolean
  onToggleSelection: () => void
}

export const DraggableUserCard = ({
  user,
  isDragOverlay = false,
  isSelected,
  onToggleSelection,
}: DraggableUserCardProps) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id: user.ref as UniqueIdentifier,
      data: {
        type: 'user',
        user,
      },
    })

  const style = transform
    ? {
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
      }
    : undefined

  const getCardStyles = () => {
    return cn(
      'p-4 bg-background border rounded-xl cursor-grab transition-all duration-200 select-none flex items-center gap-3 group',
      {
        'shadow-2xl border-primary rotate-2 scale-105 ring-2 ring-primary/20':
          isDragOverlay,
        'opacity-30': isDragging && !isDragOverlay,
        'border-primary bg-primary/5 shadow-md ring-1 ring-primary/20':
          isSelected && !isDragOverlay && !isDragging,
        'hover:shadow-md hover:border-primary/50 hover:bg-accent/50':
          !isDragOverlay && !isDragging && !isSelected,
      },
    )
  }

  const handleCheckboxChange = (e: React.MouseEvent) => {
    e.stopPropagation()
    onToggleSelection()
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={getCardStyles()}
      tabIndex={0}
      role="button"
      aria-label={`Benutzer ${user.ref} verschieben`}
    >
      {!isDragOverlay && (
        <div onClick={handleCheckboxChange} className="flex-shrink-0">
          <Checkbox
            checked={isSelected}
            onChange={() => {}}
            className="pointer-events-none"
          />
        </div>
      )}

      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-semibold text-foreground group-hover:text-primary transition-colors">
          {user.userName || 'Unbenannter Benutzer'}
        </h4>
        <div className="flex items-center gap-2 mt-1">
          <p className="text-xs text-muted-foreground">
            {user.userId || 'Kein Login'}
          </p>
          {user.ref && (
            <>
              <span className="text-xs text-muted-foreground/50">•</span>
              <p className="text-xs text-muted-foreground font-mono">
                ID: {user.ref}
              </p>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
