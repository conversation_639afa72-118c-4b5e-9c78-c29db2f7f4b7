import { create } from 'zustand'

type FilterStore = {
  searchQuery: string
  setSearchQuery: (query: string) => void

  selectedObjectGroups: string[]
  setSelectedObjectGroups: (groups: string[]) => void
  addObjectGroup: (group: string) => void
  removeObjectGroup: (group: string) => void

  selectedTypes: string[]
  setSelectedTypes: (types: string[]) => void
  addType: (type: string) => void
  removeType: (type: string) => void

  showOnlyGranted: boolean
  setShowOnlyGranted: (show: boolean) => void

  expandedGroups: Set<string>
  setExpandedGroups: (groups: Set<string>) => void
  toggleGroupExpansion: (group: string) => void
  expandAllGroups: (availableGroups?: string[]) => void
  collapseAllGroups: () => void

  clearFilters: () => void
}

export const useFilterStore = create<FilterStore>((set, get) => ({
  searchQuery: '',
  setSearchQuery: (query) => set({ searchQuery: query }),

  selectedObjectGroups: [],
  setSelectedObjectGroups: (groups) => set({ selectedObjectGroups: groups }),
  addObjectGroup: (group) =>
    set((state) => ({
      selectedObjectGroups: [...state.selectedObjectGroups, group],
    })),
  removeObjectGroup: (group) =>
    set((state) => ({
      selectedObjectGroups: state.selectedObjectGroups.filter(
        (g) => g !== group,
      ),
    })),

  selectedTypes: [],
  setSelectedTypes: (types) => set({ selectedTypes: types }),
  addType: (type) =>
    set((state) => ({
      selectedTypes: [...state.selectedTypes, type],
    })),
  removeType: (type) =>
    set((state) => ({
      selectedTypes: state.selectedTypes.filter((t) => t !== type),
    })),

  showOnlyGranted: false,
  setShowOnlyGranted: (show) => set({ showOnlyGranted: show }),

  expandedGroups: new Set(),
  setExpandedGroups: (groups) => set({ expandedGroups: groups }),
  toggleGroupExpansion: (group) =>
    set((state) => {
      const newExpanded = new Set(state.expandedGroups)
      if (newExpanded.has(group)) {
        newExpanded.delete(group)
      } else {
        newExpanded.add(group)
      }
      return { expandedGroups: newExpanded }
    }),
  expandAllGroups: (availableGroups) =>
    set((state) => {
      const groupsToExpand = availableGroups || []
      return { expandedGroups: new Set(groupsToExpand) }
    }),
  collapseAllGroups: () => set({ expandedGroups: new Set() }),

  clearFilters: () =>
    set({
      searchQuery: '',
      selectedObjectGroups: [],
      selectedTypes: [],
      showOnlyGranted: false,
      expandedGroups: new Set(),
    }),
}))
