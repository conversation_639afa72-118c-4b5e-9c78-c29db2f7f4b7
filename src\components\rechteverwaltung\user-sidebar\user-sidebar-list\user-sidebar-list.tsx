import { ScrollArea } from '@/components/ui/scroll-area'
import type { GROUP_RESPONSE_DTO } from '@/lib/backend/groups/DTOs'
import type { USER_RESPONSE_DTO, UserItem } from '@/lib/backend/user/DTOs'
import { useVirtualizer } from '@tanstack/react-virtual'
import { useRef } from 'react'
import type { GroupDialogMode } from '../group-dialog/group-dialog'
import type { UserDialogMode } from '../user-dialog/user-dialog'
import { GroupSidebarItem } from './group-sidebar-item'
import { UserSidebarItem } from './user-sidebar-item'

export const UserSidebarList = ({
  users,
  groups,
  onOpenUserDialog,
  onOpenGroupDialog,
}: {
  users: USER_RESPONSE_DTO
  groups: GROUP_RESPONSE_DTO
  onOpenUserDialog: (user: UserItem, mode: UserDialogMode) => void
  onOpenGroupDialog: (
    group: GROUP_RESPONSE_DTO[number],
    mode: GroupDialogMode,
  ) => void
}) => {


  return (
    <ScrollArea className="h-[70vh]">
      <div className="flex flex-col gap-2" >
        {users?.map((user) => (
          <UserSidebarItem
            key={user.ref}
            {...user}
            onOpenDialog={onOpenUserDialog}
          />
        ))}
        {groups?.map((group) => (
          <GroupSidebarItem
            key={group.ref}
            {...group}
            onOpenDialog={onOpenGroupDialog}
          />
        ))}
      </div>
    </ScrollArea>
  )
}
