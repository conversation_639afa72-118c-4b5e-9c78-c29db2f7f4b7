export interface iconProps {
    name: TIcon;
    color?: string;
    width?: string;
    height?: string;
    size? : string;
    viewBox?: string;
};

export interface iconPathProps {
    name: TIcon;
    color: string;
}

export type TIcon = (
    'add-photo' | 'back' | 'camera' | 'chat' | 'close' | 'contact' | 'dropdown' | 'edit' | 'enter' | 'eye' | 'list' | 'lock' | 'logout' | 'minus' | 'next' | 'pause' | 'plus' | 'scan' | 'token' | 'trash' | 'user' | 'payGrow' | 'process' | 'quote' | 'rocket' | 'time' | 'check' | 'checkmark' | 'loading' | 'success' | 'burger' | 'help' | 'placeholder' | 'settings' | 'submenu' | 'BigPacks' | 'changebox' | 'ergebnis1' | 'ergebnis2' | 'ergebnis3' | 'ergebnis4' | 'gabelstapler' | 'gitterbox' | 'LHM_simple' | 'LHM' | 'package' | 'palett' | 'pallet' | 'skip' | 'weight'
);

