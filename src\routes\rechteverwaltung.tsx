import { FilterHeader } from '@/components/rechteverwaltung/rechte/filter/filter-header'
import { RechteTreeView } from '@/components/rechteverwaltung/rechte/tree-view/rechte-tree-view'
import { UserSidebar } from '@/components/rechteverwaltung/user-sidebar/user-sidebar'
import { useGroups } from '@/lib/backend/groups/hooks'
import { groupsQueryOptions } from '@/lib/backend/groups/queries'
import { loadQueries } from '@/lib/backend/loaders'
import { useRechte } from '@/lib/backend/rechte/hooks'
import { rechteQueryOptions } from '@/lib/backend/rechte/queries'
import { useUsers } from '@/lib/backend/user/hooks'
import { usersQueryOptions } from '@/lib/backend/user/queries'
import { createFileRoute } from '@tanstack/react-router'

export const Route = createFileRoute('/rechteverwaltung')({
  component: RouteComponent,
  loader: async () => {
    await loadQueries([
      usersQueryOptions,
      rechteQueryOptions,
      groupsQueryOptions,
    ])
  },
  pendingComponent: () => <div>Loading...</div>,
  errorComponent: () => <div>Error</div>,
})

function RouteComponent() {
  const { data: users } = useUsers()
  const { data: rechte } = useRechte()
  const { data: groups } = useGroups()

  return (
    <div className="flex flex-row gap-4 w-full ">
      
      <UserSidebar users={users} groups={groups} />
      <div className="flex-1  border-gray-200 gap-4 flex flex-col">
        <FilterHeader />
        <RechteTreeView rechte={rechte} />
      </div>
    </div>
  )
}
