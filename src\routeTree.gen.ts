/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as RechteverwaltungRouteImport } from './routes/rechteverwaltung'
import { Route as IndexRouteImport } from './routes/index'

const RechteverwaltungRoute = RechteverwaltungRouteImport.update({
  id: '/rechteverwaltung',
  path: '/rechteverwaltung',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/rechteverwaltung': typeof RechteverwaltungRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/rechteverwaltung': typeof RechteverwaltungRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/rechteverwaltung': typeof RechteverwaltungRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/rechteverwaltung'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/rechteverwaltung'
  id: '__root__' | '/' | '/rechteverwaltung'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  RechteverwaltungRoute: typeof RechteverwaltungRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/rechteverwaltung': {
      id: '/rechteverwaltung'
      path: '/rechteverwaltung'
      fullPath: '/rechteverwaltung'
      preLoaderRoute: typeof RechteverwaltungRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  RechteverwaltungRoute: RechteverwaltungRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
