import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { useGroups } from '@/lib/backend/groups/hooks'
import type { RECHTE_RESPONSE_DTO } from '@/lib/backend/rechte/DTOs'
import { useRechteGroup, useRechteUser } from '@/lib/backend/rechte/hooks'
import { useUsers } from '@/lib/backend/user/hooks'
import { Filter, Search, UserCheck, Users } from 'lucide-react'
import { useMemo, useState } from 'react'

type FilterType = 'all' | 'users' | 'groups'

export const RechteUserDialog = ({
  open,
  onOpenChange,
  permission,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
  permission: RECHTE_RESPONSE_DTO[number]
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<FilterType>('all')
  const [showActiveOnly, setShowActiveOnly] = useState(false)

  const { data: users } = useUsers()
  const { data: groups } = useGroups()

  const { data: usersWithRecht } = useRechteUser(permission.ref || 0)
  const { data: groupsWithRecht } = useRechteGroup(permission.ref || 0)

  const filteredData = useMemo(() => {
    const filteredUsers =
      users?.filter((user) => {
        const matchesSearch =
          (user.userName?.toLowerCase().includes(searchTerm.toLowerCase()) ??
            false) ||
          (user.userId?.toLowerCase().includes(searchTerm.toLowerCase()) ??
            false) ||
          (user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ??
            false)

        const hasPermission = showActiveOnly
          ? (usersWithRecht?.some(
              (userWithRecht) => userWithRecht.refBen === user.ref,
            ) ?? false)
          : true

        return matchesSearch && hasPermission
      }) || []

    const filteredGroups =
      groups?.filter((group) => {
        const matchesSearch =
          (group.groupName?.toLowerCase().includes(searchTerm.toLowerCase()) ??
            false) ||
          (group.groupId?.toLowerCase().includes(searchTerm.toLowerCase()) ??
            false)

        const hasPermission = showActiveOnly
          ? (groupsWithRecht?.some(
              (groupWithRecht) => groupWithRecht.refGrp === group.ref,
            ) ?? false)
          : true

        return matchesSearch && hasPermission
      }) || []

    return { filteredUsers, filteredGroups }
  }, [
    users,
    groups,
    searchTerm,
    showActiveOnly,
    usersWithRecht,
    groupsWithRecht,
  ])

  const usersWithPermissions = filteredData.filteredUsers.filter((user) =>
    usersWithRecht?.some((userWithRecht) => userWithRecht.refBen === user.ref),
  )

  const groupsWithPermissions = filteredData.filteredGroups.filter((group) =>
    groupsWithRecht?.some(
      (groupWithRecht) => groupWithRecht.refGrp === group.ref,
    ),
  )

  const totalWithPermissions =
    usersWithPermissions.length + groupsWithPermissions.length
  const totalFiltered =
    filteredData.filteredUsers.length + filteredData.filteredGroups.length

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl h-[90vh] flex flex-col p-0">
        <div className="p-6 border-b flex-shrink-0">
          <DialogHeader className="space-y-4">
            <DialogTitle className="text-2xl font-bold text-center">
              Berechtigung: {permission.objectName}
            </DialogTitle>

            <div className="flex flex-col space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Nutzer oder Gruppen suchen..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-12 text-base"
                />
              </div>

              <div className="flex flex-wrap items-center gap-3">
                <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Filter:</span>
                </div>

                <div className="flex gap-2">
                  {(['all', 'users', 'groups'] as const).map((type) => (
                    <Button
                      key={type}
                      variant={filterType === type ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setFilterType(type)}
                      className="h-8"
                    >
                      {type === 'all' && 'Alle'}
                      {type === 'users' && 'Nur Nutzer'}
                      {type === 'groups' && 'Nur Gruppen'}
                    </Button>
                  ))}
                </div>

                <Separator orientation="vertical" className="h-6" />

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="active-only"
                    checked={showActiveOnly}
                    onCheckedChange={(checked) =>
                      setShowActiveOnly(checked === true)
                    }
                  />
                  <label
                    htmlFor="active-only"
                    className="text-sm font-medium cursor-pointer"
                  >
                    Nur berechtigte Nutzer/Gruppen
                  </label>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex gap-3">
                  <Badge variant="outline" className="gap-1">
                    <Users className="h-3 w-3" />
                    {totalFiltered} gefiltert
                  </Badge>
                  <Badge variant="default" className="gap-1">
                    <UserCheck className="h-3 w-3" />
                    {totalWithPermissions} mit Berechtigung
                  </Badge>
                </div>
              </div>
            </div>
          </DialogHeader>
        </div>

        <ScrollArea className="flex-1 overflow-auto p-6">
          <div className="space-y-6">
            {(filterType === 'all' || filterType === 'users') && (
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Users className="h-5 w-5 text-primary" />
                    <h3 className="text-lg font-semibold">
                      Nutzer ({filteredData.filteredUsers.length})
                    </h3>
                  </div>

                  {filteredData.filteredUsers.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      Keine Nutzer gefunden
                    </div>
                  ) : (
                    <div className="grid gap-3">
                      {filteredData.filteredUsers.map((user) => {
                        const hasPermission =
                          usersWithRecht?.some(
                            (userWithRecht) =>
                              userWithRecht.refBen === user.ref,
                          ) ?? false

                        return (
                          <div
                            key={`user-${user.ref}`}
                            className={`
                              flex items-center justify-between p-4 rounded-xl border-2 transition-all duration-200 cursor-pointer
                              ${
                                hasPermission
                                  ? 'bg-green-50 border-green-200 hover:bg-green-100'
                                  : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                              }
                            `}
                          >
                            <div className="flex flex-col space-y-1">
                              <p className="font-semibold text-base">
                                {user.userName || `Kein Name (${user.userId})`}
                              </p>
                              <div className="flex items-center gap-3 text-sm text-muted-foreground">
                                <span>ID: {user.userId}</span>

                                {user.isAdmin && (
                                  <Badge
                                    variant="destructive"
                                    className="text-xs"
                                  >
                                    Admin
                                  </Badge>
                                )}
                                {user.status && (
                                  <Badge
                                    variant={
                                      user.status === 'aktiv'
                                        ? 'default'
                                        : 'secondary'
                                    }
                                    className="text-xs"
                                  >
                                    {user.status}
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <Checkbox
                              checked={hasPermission}
                              className="scale-125"
                            />
                          </div>
                        )
                      })}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {(filterType === 'all' || filterType === 'groups') && (
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Users className="h-5 w-5 text-primary" />
                    <h3 className="text-lg font-semibold">
                      Gruppen ({filteredData.filteredGroups.length})
                    </h3>
                  </div>

                  {filteredData.filteredGroups.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      Keine Gruppen gefunden
                    </div>
                  ) : (
                    <div className="grid gap-3">
                      {filteredData.filteredGroups.map((group) => {
                        const hasPermission =
                          groupsWithRecht?.some(
                            (groupWithRecht) =>
                              groupWithRecht.refGrp === group.ref,
                          ) ?? false

                        return (
                          <div
                            key={`group-${group.ref}`}
                            className={`
                              flex items-center justify-between p-4 rounded-xl border-2 transition-all duration-200 cursor-pointer
                              ${
                                hasPermission
                                  ? 'bg-blue-50 border-blue-200 hover:bg-blue-100'
                                  : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                              }
                            `}
                          >
                            <div className="flex flex-col space-y-1">
                              <p className="font-semibold text-base">
                                {group.groupName ||
                                  `Kein Name (${group.groupId})`}
                              </p>
                              <div className="flex items-center gap-3 text-sm text-muted-foreground">
                                <span>ID: {group.groupId}</span>
                                {group.adminFlag && (
                                  <Badge
                                    variant="destructive"
                                    className="text-xs"
                                  >
                                    Admin-Gruppe
                                  </Badge>
                                )}
                                {group.defaultGroup && (
                                  <Badge variant="default" className="text-xs">
                                    Standard
                                  </Badge>
                                )}
                                {group.groupArt && (
                                  <Badge
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    {group.groupArt}
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <Checkbox
                              checked={hasPermission}
                              className="scale-125"
                            />
                          </div>
                        )
                      })}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
