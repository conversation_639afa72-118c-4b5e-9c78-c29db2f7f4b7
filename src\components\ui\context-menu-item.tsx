import { ContextMenuItem } from "./context-menu"

type CustomContextMenuItemProps = {
  onClick: () => void
  icon: React.ReactNode
  label: string
}

export const CustomContextMenuItem = ({ onClick, icon, label }: CustomContextMenuItemProps) => {
  return (
    <ContextMenuItem
      onClick={onClick}
      className="flex items-center gap-3 px-3 py-2.5 rounded-lg hover:bg-gradient-to-r hover:from-[#57DD00]/10 hover:to-emerald-50 transition-all duration-200 cursor-pointer group"
    >
      <div className="p-1.5 rounded-md bg-gray-100/80 group-hover:bg-[#57DD00]/20 transition-colors duration-200">
        {icon}
      </div>
      <span className="text-sm font-medium text-gray-700 group-hover:text-gray-900">
        {label}
      </span>
    </ContextMenuItem>
  )
}
