export const GROUP_NAMES: Record<string, string> = {
  ARTIKEL: 'Artikel',
  AUFTRAG: 'AUFTRAG',
  BACK: 'BACK',
  BATCH: 'BATCH',
  BEN: 'Benutzer',
  BERICHT: 'BERICHT',
  BESCHAFFUNG: 'BESCHAFFUNG',
  BEST: 'BESTELLUNGEN',
  BESTAND: 'BESTAND',
  BESTELL: 'BESTELL',
  BESTELLUNG: 'BESTELLUNG',
  CONFIG: 'CONFIG',
  DASHBOARD: 'DASHBOARD',
  DEPOT: 'DEPOT',
  DRUCKEN: 'DRUCKEN',
  EAN: 'EAN',
  EINLAGER: 'EINLAGER',
  EMPF: 'EMPF',
  <PERSON>VO<PERSON>AN: 'EVOSCAN',
  EXPORT: 'EXPORT',
  HACCP: 'HACCP',
  IMPORT: 'IMPORT',
  INTERFACE: 'INTERFACE',
  INV: 'INV',
  INVENTUR: 'INVENTUR',
  KOMM: 'KOMM',
  LAGER: 'LAGER',
  LE: 'LE',
  LEERGUT: 'LEERGUT',
  LIEFERANT: 'LIEFERANT',
  LIEFERUNG: 'LIEFERUNG',
  LIFE: 'LIFE',
  LIZENZEN: 'LIZENZEN',
  LP: 'LAGERPLATZ',
  MANDANT: 'MANDANT',
  MDE: 'MDE',
  MHD: 'MHD',
  NACHSCHUB: 'NACHSCHUB',
  NVE: 'NVE',
  OMS: 'OMS',
  PACK: 'PACKEN',
  PLANUNG: 'PLANUNG',
  PRINTER: 'PRINTER',
  PROTOKOLL: 'PROTOKOLL',
  QS: 'QS',
  RELATION: 'RELATION',
  RETOURE: 'RETOURE',
  RETOUREN: 'RETOUREN',
  REVISION: 'REVISION',
  SENDIT: 'SENDIT',
  SETUP: 'SETUP',
  SPEDITION: 'SPEDITION',
  STAMMDATEN: 'STAMMDATEN',
  TEST: 'TEST',
  TOUR: 'TOUR',
  TRANSPORT: 'TRANSPORT',
  VAS: 'VAS',
  VERLADEN: 'VERLADEN',
  VERPACKEN: 'VERPACKEN',
  VERSAND: 'VERSAND',
  VERWALTUNG: 'VERWALTUNG',
  VORRES: 'VORRES',
  WA: 'WARENAUSGANG',
  WE: 'WARENEINANG',
  WW: 'WW',
  ZOLL: 'ZOLL',
}

export const permission_buttons = [
  { key: 'read', title: 'Read', label: 'R' },
  { key: 'write', title: 'Write', label: 'W' },
  { key: 'execute', title: 'Execute', label: 'E' },
  { key: 'admin', title: 'Admin', label: 'A' },
  { key: 'grant', title: 'Grant', label: 'G' },
]

export const PERMISSION_KEY_TO_FLAG: Record<
  string,
  'R' | 'W' | 'E' | 'A' | 'G'
> = {
  read: 'R',
  write: 'W',
  execute: 'E',
  admin: 'A',
  grant: 'G',
}
