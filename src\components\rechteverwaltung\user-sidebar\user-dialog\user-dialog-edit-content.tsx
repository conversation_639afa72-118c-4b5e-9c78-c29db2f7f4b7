import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import type { UPDATE_USER_REQUEST_DTO, UserItem } from '@/lib/backend/user/DTOs'
import { useUpdateUser } from '@/lib/backend/user/hooks'
import {
  AtSign,
  Badge,
  Building2,
  Hash,
  KeyRound,
  Mail,
  Save,
  ShieldCheck,
  User2,
} from 'lucide-react'
import { useState } from 'react'
import { toast } from 'sonner'
import { UserDialogFirmaSelect } from './user-dialog-firma-select'

interface UserDialogEditContentProps {
  user: UserItem
  onClose: () => void
}

export const UserDialogEditContent = ({
  user,
  onClose,
}: UserDialogEditContentProps) => {
  const [updateUser, setUpdateUser] = useState<UPDATE_USER_REQUEST_DTO>({
    userRef: user.ref!,
    id: user.userId || '',
    name: user.userName || '',
    password: '', // Password wird leer gelassen, da wir es nicht anzeigen
    shortName: user.userShortName || '',
    numberId: user.userNumId || '',
    title: user.userTitle || '',
    email: user.email || '',
    companyRef: user.refFirma || 0,
    isAdmin: user.isAdmin || false,
  })

  const updateUserMutation = useUpdateUser()

  const handleInputChange = (
    field: keyof UPDATE_USER_REQUEST_DTO,
    value: string | number | boolean,
  ) => {
    setUpdateUser((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleUpdateUserSubmit = async () => {
    try {
      await updateUserMutation.mutateAsync(updateUser)
      onClose()
      toast.success('Benutzer erfolgreich aktualisiert.')
    } catch (error) {
      console.error('Fehler beim Aktualisieren des Benutzers:', error)
      toast.error('Fehler beim Aktualisieren des Benutzers')
    }
  }

  const isLoading = updateUserMutation.isPending

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-8">
      <div className="relative">
        <div className="flex items-center gap-6 pb-6 border-b border-border/50">
          <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center ring-1 ring-border/20">
            <User2 className="w-8 h-8 text-primary" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-2xl font-semibold text-foreground tracking-tight">
                Benutzer bearbeiten
              </h1>
            </div>
            <p className="text-sm text-muted-foreground">
              Bearbeiten Sie die Benutzerdaten von {user.userName}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-6">
          {/* Benutzer-ID */}
          <div className="space-y-2">
            <Label htmlFor="id" className="flex items-center gap-2 text-sm font-medium">
              <AtSign className="w-4 h-4 text-muted-foreground" />
              Benutzer-ID
            </Label>
            <Input
              id="id"
              value={updateUser.id}
              onChange={(e) => handleInputChange('id', e.target.value)}
              placeholder="Eindeutige Benutzer-ID"
              className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
            />
          </div>

          {/* Name */}
          <div className="space-y-2">
            <Label htmlFor="name" className="flex items-center gap-2 text-sm font-medium">
              <User2 className="w-4 h-4 text-muted-foreground" />
              Vollständiger Name
            </Label>
            <Input
              id="name"
              value={updateUser.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Vor- und Nachname"
              className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
            />
          </div>

          {/* Kurzer Name */}
          <div className="space-y-2">
            <Label htmlFor="shortName" className="flex items-center gap-2 text-sm font-medium">
              <Badge className="w-4 h-4 text-muted-foreground" />
              Kurzer Name
            </Label>
            <Input
              id="shortName"
              value={updateUser.shortName}
              onChange={(e) => handleInputChange('shortName', e.target.value)}
              placeholder="Kürzel oder Initialen"
              className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
            />
          </div>

          {/* Nummer-ID */}
          <div className="space-y-2">
            <Label htmlFor="numberId" className="flex items-center gap-2 text-sm font-medium">
              <Hash className="w-4 h-4 text-muted-foreground" />
              Nummer-ID
            </Label>
            <Input
              id="numberId"
              value={updateUser.numberId}
              onChange={(e) => handleInputChange('numberId', e.target.value)}
              placeholder="Numerische Identifikation"
              className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
            />
          </div>

          {/* Titel */}
          <div className="space-y-2">
            <Label htmlFor="title" className="flex items-center gap-2 text-sm font-medium">
              <ShieldCheck className="w-4 h-4 text-muted-foreground" />
              Titel
            </Label>
            <Input
              id="title"
              value={updateUser.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="Berufstitel oder Position"
              className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
            />
          </div>

          {/* E-Mail */}
          <div className="space-y-2">
            <Label htmlFor="email" className="flex items-center gap-2 text-sm font-medium">
              <Mail className="w-4 h-4 text-muted-foreground" />
              E-Mail-Adresse
            </Label>
            <Input
              id="email"
              type="email"
              value={updateUser.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="<EMAIL>"
              className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
            />
          </div>

          {/* Passwort */}
          <div className="space-y-2">
            <Label htmlFor="password" className="flex items-center gap-2 text-sm font-medium">
              <KeyRound className="w-4 h-4 text-muted-foreground" />
              Neues Passwort (optional)
            </Label>
            <Input
              id="password"
              type="password"
              value={updateUser.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              placeholder="Leer lassen, um Passwort nicht zu ändern"
              className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
            />
          </div>

          {/* Firma */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2 text-sm font-medium">
              <Building2 className="w-4 h-4 text-muted-foreground" />
              Firma
            </Label>
            <UserDialogFirmaSelect
              value={updateUser.companyRef}
              onValueChange={(value) => handleInputChange('companyRef', value)}
            />
          </div>

          {/* Admin-Status */}
          <div className="flex items-center space-x-2 pt-6">
            <Checkbox
              id="isAdmin"
              checked={updateUser.isAdmin}
              onCheckedChange={(checked) => handleInputChange('isAdmin', checked as boolean)}
            />
            <Label
              htmlFor="isAdmin"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Administrator-Rechte
            </Label>
          </div>
        </div>

        <div className="pt-6 border-t border-border/30 flex gap-3">
          <Button
            onClick={handleUpdateUserSubmit}
            className="flex-1 gap-2"
            size="lg"
            disabled={isLoading}
          >
            <Save className="w-4 h-4" />
            {isLoading ? 'Aktualisiere Benutzer...' : 'Änderungen speichern'}
          </Button>
          <Button
            onClick={onClose}
            variant="outline"
            size="lg"
            disabled={isLoading}
          >
            Abbrechen
          </Button>
        </div>
      </div>
    </div>
  )
}
