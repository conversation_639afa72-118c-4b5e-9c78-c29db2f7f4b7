import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import type { GROUP_RESPONSE_DTO } from '@/lib/backend/groups/DTOs'
import { Building, Users } from 'lucide-react'
import { GroupDialogLocationSelect } from '../group-dialog/group-dialog-location-select'

interface UserDialogGroupContentProps {
  group: GROUP_RESPONSE_DTO[number]
  onClose: () => void
}

export const UserDialogGroupContent = ({
  group,
}: UserDialogGroupContentProps) => {
  const gruppentypen = [
    { id: 'ACO', label: 'ACO-Gruppe', value: 'ACO' },
    { id: 'KOMM', label: 'KOMM-Gruppe', value: 'KOMM' },
    {
      id: 'NACHSCHUB',
      label: 'Nachschub-Gruppe',
      value: 'Nachschub',
    },
    {
      id: 'TRANSPORT',
      label: 'Transport-Gruppe',
      value: 'Transport',
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <div className="w-16 h-16 rounded-2xl bg-primary-gradient flex items-center justify-center text-black">
          <Users className="h-8 w-8" />
        </div>
        <div>
          <h2 className="text-2xl font-bold">
            {group.groupName || 'Unbenannte Gruppe'}
          </h2>
          <p className="text-gray-500">Gruppe • ID: {group.groupId}</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="h-4 w-4" />
            Gruppeninformationen
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={group.adminFlag}
                  id="admin-group-checkbox"
                  disabled
                />
                <Label
                  htmlFor="admin-group-checkbox"
                  className="text-sm font-medium"
                >
                  Admin-Gruppe
                </Label>
              </div>

              <div className="space-y-2">
                <Label htmlFor="group-name" className="text-sm font-medium">
                  Name
                </Label>
                <Input
                  id="group-name"
                  value={group.groupId || ''}
                  placeholder="Nicht verfügbar"
                  disabled
                  className="bg-gray-50"
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="group-description"
                  className="text-sm font-medium"
                >
                  Beschreibung
                </Label>
                <Input
                  id="group-description"
                  value={group.groupName || ''}
                  placeholder="Keine Beschreibung verfügbar"
                  disabled
                  className="bg-gray-50"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Gültig für Niederlassung
                </Label>
                <GroupDialogLocationSelect
                  defaultValue={group.refLocation?.toString() || 'all'}
                />
              </div>
            </div>

            <div className="space-y-4">
              <Label className="text-sm font-medium">Gruppentyp</Label>
              <div className="grid grid-cols-1 gap-2">
                {gruppentypen.map((typ) => (
                  <Button
                    key={typ.id}
                    variant={
                      group.groupArt === typ.value ? 'default' : 'outline'
                    }
                    className={`justify-start h-auto p-3 ${
                      group.groupArt === typ.value
                        ? 'bg-primary-gradient text-black'
                        : 'bg-white hover:bg-gray-50'
                    }`}
                    disabled
                  >
                    <div className="flex items-center space-x-2">
                      <div
                        className={`w-2 h-2 rounded-full ${
                          group.groupArt === typ.value
                            ? 'bg-white'
                            : 'bg-gray-400'
                        }`}
                      />
                      <span className="text-sm">{typ.label}</span>
                    </div>
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {group.defaultGroup && (
            <div className="mt-6 pt-4 border-t">
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 border-green-200"
              >
                Standard-Gruppe
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
