import { Button } from '@/components/ui/button'
import type { GROUP_RESPONSE_DTO } from '@/lib/backend/groups/DTOs'
import { useGroupUsers } from '@/lib/backend/groups/hooks'
import { useUsers } from '@/lib/backend/user/hooks'
import {
  DndContext,
  type DragEndEvent,
  type DragOverEvent,
  DragOverlay,
  type DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  type UniqueIdentifier,
  closestCenter,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import { sortableKeyboardCoordinates } from '@dnd-kit/sortable'
import { ArrowLeft, ArrowRight, UserPlus, Users } from 'lucide-react'
import { useCallback, useMemo, useState } from 'react'
import { createPortal } from 'react-dom'
import { DraggableUserCard } from './group-dialog-draggable-user-card'
import { UserContainer } from './group-dialog-user-container'

type GroupDialogContentChangeUsersProps = {
  group: GROUP_RESPONSE_DTO[number]
}

export const GroupDialogContentChangeUsers: React.FC<
  GroupDialogContentChangeUsersProps
> = ({ group }) => {
  if (!group || !group.ref) return null

  const { data: allUsers } = useUsers()
  const { data: groupUsers } = useGroupUsers(group.ref)

  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null)
  const [isDragOver, setIsDragOver] = useState<{
    container: string | null
    isOver: boolean
  }>({ container: null, isOver: false })

  const [selectedAvailable, setSelectedAvailable] = useState<Set<number>>(
    new Set(),
  )
  const [selectedAssigned, setSelectedAssigned] = useState<Set<number>>(
    new Set(),
  )

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  const { availableUsers, assignedUsers } = useMemo(() => {
    if (!allUsers || !groupUsers) {
      return { availableUsers: [], assignedUsers: [] }
    }

    const assignedUserRefs = new Set(groupUsers.map((user) => user.refBen))

    return {
      availableUsers: allUsers.filter(
        (user) => !assignedUserRefs.has(user.ref),
      ),
      assignedUsers: groupUsers
        .map((groupUser) => ({
          ref: groupUser.refBen,
          name: groupUser.userName,
          login: groupUser.userId,
        }))
        .filter((user) => user.ref !== undefined),
    }
  }, [allUsers, groupUsers])

  const activeUser = useMemo(() => {
    if (!activeId) return null
    return [...availableUsers, ...assignedUsers].find(
      (user) => user.ref === activeId,
    )
  }, [activeId, availableUsers, assignedUsers])

  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id)
  }, [])

  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { over } = event

    if (over && (over.id === 'available' || over.id === 'assigned')) {
      setIsDragOver({
        container: over.id as string,
        isOver: true,
      })
    } else {
      setIsDragOver({ container: null, isOver: false })
    }
  }, [])

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event

      setActiveId(null)
      setIsDragOver({ container: null, isOver: false })

      if (!over || !active) return

      const draggedUserRef = active.id as number
      const targetContainer = over.id as string

      const isCurrentlyInAvailable = availableUsers.some(
        (u) => u.ref === draggedUserRef,
      )
      const isCurrentlyInAssigned = assignedUsers.some(
        (u) => u.ref === draggedUserRef,
      )

      if (isCurrentlyInAvailable && targetContainer === 'assigned') {
        handleUserAssignment(draggedUserRef, 'assign')
      } else if (isCurrentlyInAssigned && targetContainer === 'available') {
        handleUserAssignment(draggedUserRef, 'remove')
      }
    },
    [availableUsers, assignedUsers],
  )

  const handleUserAssignment = useCallback(
    (userRef: number, action: 'assign' | 'remove') => {
      const user = [...availableUsers, ...assignedUsers].find(
        (u) => u.ref === userRef,
      )

      if (!user) return

      // TODO: Hier würde später die echte API-Mutation aufgerufen werden
      // Beispiel:
      // if (action === 'assign') {
      //   await assignUserToGroup.mutateAsync({ groupRef: group.ref, userRef })
      // } else {
      //   await removeUserFromGroup.mutateAsync({ groupRef: group.ref, userRef })
      // }
    },
    [availableUsers, assignedUsers, group],
  )

  const handleBulkAssign = useCallback(() => {
    selectedAvailable.forEach((userRef) => {
      handleUserAssignment(userRef, 'assign')
    })
    setSelectedAvailable(new Set())
  }, [selectedAvailable, handleUserAssignment])

  const handleBulkRemove = useCallback(() => {
    selectedAssigned.forEach((userRef) => {
      handleUserAssignment(userRef, 'remove')
    })
    setSelectedAssigned(new Set())
  }, [selectedAssigned, handleUserAssignment])

  const toggleAvailableSelection = useCallback((userRef: number) => {
    setSelectedAvailable((prev) => {
      const newSet = new Set(prev)
      if (newSet.has(userRef)) {
        newSet.delete(userRef)
      } else {
        newSet.add(userRef)
      }
      return newSet
    })
  }, [])

  const toggleAssignedSelection = useCallback((userRef: number) => {
    setSelectedAssigned((prev) => {
      const newSet = new Set(prev)
      if (newSet.has(userRef)) {
        newSet.delete(userRef)
      } else {
        newSet.add(userRef)
      }
      return newSet
    })
  }, [])

  const selectAllAvailable = useCallback(() => {
    setSelectedAvailable(
      new Set(
        availableUsers
          .filter((u) => u.ref !== undefined)
          .map((u) => u.ref as number),
      ),
    )
  }, [availableUsers])

  const selectAllAssigned = useCallback(() => {
    setSelectedAssigned(
      new Set(
        assignedUsers
          .filter((u) => u.ref !== undefined)
          .map((u) => u.ref as number),
      ),
    )
  }, [assignedUsers])

  const clearAvailableSelection = useCallback(() => {
    setSelectedAvailable(new Set())
  }, [])

  const clearAssignedSelection = useCallback(() => {
    setSelectedAssigned(new Set())
  }, [])

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
      accessibility={{
        restoreFocus: true,
      }}
    >
      <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <UserContainer
            id="available"
            title="Verfügbare Benutzer"
            icon={<Users className="w-5 h-5" />}
            users={availableUsers}
            isDropTarget={isDragOver.container === 'available'}
            isEmpty={availableUsers.length === 0}
            selectedUsers={selectedAvailable}
            onToggleSelection={toggleAvailableSelection}
            onSelectAll={selectAllAvailable}
            onClearSelection={clearAvailableSelection}
          />
          <UserContainer
            id="assigned"
            title="Zugewiesene Benutzer"
            icon={<UserPlus className="w-5 h-5" />}
            users={assignedUsers}
            isDropTarget={isDragOver.container === 'assigned'}
            isEmpty={assignedUsers.length === 0}
            selectedUsers={selectedAssigned}
            onToggleSelection={toggleAssignedSelection}
            onSelectAll={selectAllAssigned}
            onClearSelection={clearAssignedSelection}
          />
        </div>
        <div className="flex items-center justify-center gap-4 py-6">
          <Button
            variant="outline"
            size="sm"
            onClick={handleBulkAssign}
            disabled={selectedAvailable.size === 0}
            className="flex items-center gap-2"
          >
            <ArrowRight className="w-4 h-4" />
            Zuweisen ({selectedAvailable.size})
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleBulkRemove}
            disabled={selectedAssigned.size === 0}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Entfernen ({selectedAssigned.size})
          </Button>
        </div>
      </div>

      {createPortal(
        <DragOverlay>
          {activeUser ? (
            <DraggableUserCard
              user={activeUser}
              isDragOverlay
              isSelected={false}
              onToggleSelection={() => {}}
            />
          ) : null}
        </DragOverlay>,
        document.body,
      )}
    </DndContext>
  )
}
