import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import type { CREATE_GROUP_REQUEST_DTO } from '@/lib/backend/groups/DTOs'
import { useCreateGroup } from '@/lib/backend/groups/hooks'
import { useLocations } from '@/lib/backend/locations/hooks'
import { Building2, Hash, MapPin, Save, Shield, Tag, Users } from 'lucide-react'
import { useState } from 'react'

export const AddDialogCreateGroupContent = () => {
  const [newGroup, setNewGroup] = useState<CREATE_GROUP_REQUEST_DTO>({
    groupArt: '',
    groupId: '',
    groupName: '',
    locationReference: null,
    isAdmin: false,
  })

  const createGroupMutation = useCreateGroup()
  const { data: locations } = useLocations()

  const gruppentypen = [
    { id: 'ACO', label: 'ACO-Gruppe', value: 'ACO' },
    { id: 'KOMM', label: 'KOMM-Gruppe', value: 'KOMM' },
    { id: 'NACHSCHUB', label: 'Nachschub-Gruppe', value: 'Nachschub' },
    { id: 'TRANSPORT', label: 'Transport-Gruppe', value: 'Transport' },
  ]

  const handleInputChange = (
    field: keyof CREATE_GROUP_REQUEST_DTO,
    value: string | number | boolean | null,
  ) => {
    setNewGroup((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSubmit = async () => {
    try {
      await createGroupMutation.mutateAsync(newGroup)

      setNewGroup({
        groupArt: '',
        groupId: '',
        groupName: '',
        locationReference: null,
        isAdmin: false,
      })
    } catch (error) {
      console.error('Fehler beim Erstellen der Gruppe:', error)
    }
  }

  const isFormValid = newGroup.groupId.trim() !== '' && newGroup.groupArt !== ''

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
      <div className="flex items-center gap-6 pb-6 border-b border-border/50">
        <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950/50 dark:to-emerald-900/50 flex items-center justify-center ring-1 ring-emerald-200/50 dark:ring-emerald-800/50">
          <Users className="w-8 h-8 text-emerald-600 dark:text-emerald-400" />
        </div>
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-semibold text-foreground tracking-tight mb-2">
            Neue Gruppe erstellen
          </h1>
          <p className="text-muted-foreground">
            Erstelle eine neue Benutzergruppe zur Organisation von
            Berechtigungen.
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Tag className="w-5 h-5 text-primary" />
              Grundinformationen
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label
                htmlFor="groupId"
                className="text-sm font-medium flex items-center gap-2"
              >
                <Hash className="w-4 h-4" />
                Gruppen-ID*
              </Label>
              <Input
                id="groupId"
                type="text"
                value={newGroup.groupId}
                onChange={(e) => handleInputChange('groupId', e.target.value)}
                placeholder="GRUPPE_001"
                disabled={createGroupMutation.isPending}
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="groupName"
                className="text-sm font-medium flex items-center gap-2"
              >
                <Users className="w-4 h-4" />
                Gruppenname
              </Label>
              <Input
                id="groupName"
                type="text"
                value={newGroup.groupName || ''}
                onChange={(e) =>
                  handleInputChange('groupName', e.target.value || null)
                }
                placeholder="Administrator, Benutzer, etc."
                disabled={createGroupMutation.isPending}
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                Niederlassung
              </Label>
              <Select
                value={newGroup.locationReference?.toString() || 'ALL'}
                onValueChange={(value) =>
                  handleInputChange(
                    'locationReference',
                    value === 'ALL' ? null : parseInt(value),
                  )
                }
                disabled={createGroupMutation.isPending}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Niederlassung auswählen" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL"> Alle Niederlassungen </SelectItem>
                  {locations
                    ?.filter((location) => location.ref != null)
                    .map((location) => (
                      <SelectItem
                        key={location.ref}
                        value={location.ref!.toString()}
                      >
                        {location.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2 pt-2">
              <Checkbox
                id="isAdmin"
                checked={newGroup.isAdmin}
                onCheckedChange={(checked) =>
                  handleInputChange('isAdmin', !!checked)
                }
                disabled={createGroupMutation.isPending}
              />
              <Label
                htmlFor="isAdmin"
                className="text-sm font-medium flex items-center gap-2"
              >
                <Shield className="w-4 h-4" />
                Admin-Gruppe
              </Label>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="w-5 h-5 text-primary" />
              Gruppentyp*
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {gruppentypen.map((typ) => (
                <Button
                  key={typ.id}
                  variant={
                    newGroup.groupArt === typ.value ? 'default' : 'outline'
                  }
                  className={`w-full justify-start h-auto p-4 ${
                    newGroup.groupArt === typ.value
                      ? 'bg-primary-gradient text-black'
                      : 'bg-background hover:bg-muted'
                  }`}
                  onClick={() => handleInputChange('groupArt', typ.value)}
                  disabled={createGroupMutation.isPending}
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className={`w-3 h-3 rounded-full ${
                        newGroup.groupArt === typ.value
                          ? 'bg-primary-foreground'
                          : 'bg-muted-foreground'
                      }`}
                    />
                    <span className="text-sm font-medium">{typ.label}</span>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="pt-6 border-t border-border/30">
        <Button
          onClick={handleSubmit}
          className="w-full gap-2"
          size="lg"
          disabled={createGroupMutation.isPending || !isFormValid}
        >
          <Save className="w-4 h-4" />
          {createGroupMutation.isPending
            ? 'Erstelle Gruppe...'
            : 'Gruppe erstellen'}
        </Button>

        {!isFormValid && (
          <p className="text-sm text-muted-foreground mt-2 text-center">
            Bitte füllen Sie alle Pflichtfelder (*) aus.
          </p>
        )}
      </div>
    </div>
  )
}
