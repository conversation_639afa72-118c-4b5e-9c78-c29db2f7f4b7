import { ContextMenuContent } from '@/components/ui/context-menu'
import { CustomContextMenuItem } from '@/components/ui/context-menu-item'
import type { UserItem } from '@/lib/backend/user/DTOs'
import { Edit, Eye, PlusCircle, Users, UserSquare } from 'lucide-react'
import type { UserDialogMode } from '../user-dialog/user-dialog'

export const UserSidebarContextMenuContent = ({
  user,
  onOpenDialog,
}: {
  user: UserItem
  onOpenDialog: (user: UserItem, mode: UserDialogMode) => void
}) => {
  return (
    <ContextMenuContent>
      <div className="flex flex-col items-center gap-2 p-2 border-b">
        <span className="font-semibold">{user.userName}</span>
        <span className="text-xs text-gray-500">Benutzer</span>
      </div>
      <CustomContextMenuItem
        label="Details anzeigen"
        icon={<Eye />}
        onClick={() => {
          onOpenDialog(user, 'details')
        }}
      />
      <CustomContextMenuItem
        label="Bearbeiten"
        icon={<Edit />}
        onClick={() => {
          onOpenDialog(user, 'edit')
        }}
      />
      <CustomContextMenuItem
        label="Gruppe zuweisen"
        icon={<Users />}
        onClick={() => {
          onOpenDialog(user, 'assign-group')
        }}
      />
      <CustomContextMenuItem
        label="Nutzer vergleichen"
        icon={<UserSquare />}
        onClick={() => {
          onOpenDialog(user, 'user-compare')
        }}
      />
      <CustomContextMenuItem
        label="Kopieren"
        icon={<PlusCircle />}
        onClick={() => {
          // onOpenDialog(user, 'create-user')
        }}
      />
    </ContextMenuContent>
  )
}
