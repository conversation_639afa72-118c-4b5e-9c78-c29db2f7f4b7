import type { RECHTE_RESPONSE_DTO } from '@/lib/backend/rechte/DTOs'
import type {
  USER_GROUPS_RECHTE_RESPONSE_DTO,
  USER_RECHTE_RESPONSE_DTO,
} from '@/lib/backend/user/DTOs'
import { useMemo } from 'react'
import { useFilterStore } from './useFilterStore'

export const useRechteFilter = (
  rechte: RECHTE_RESPONSE_DTO,
  userRechte: USER_RECHTE_RESPONSE_DTO,
  groupRechte: USER_GROUPS_RECHTE_RESPONSE_DTO,
) => {
  const { searchQuery, selectedObjectGroups, selectedTypes, showOnlyGranted } =
    useFilterStore()

  const filteredRechte = useMemo(() => {
    let filtered = rechte

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim()
      filtered = filtered.filter(
        (recht) =>
          recht.objectName?.toLowerCase().includes(query) ||
          recht.objectGroup?.toLowerCase().includes(query) ||
          recht.name?.toLowerCase().includes(query) ||
          recht.typ?.toLowerCase().includes(query) ||
          recht.beschreibung?.toLowerCase().includes(query),
      )
    }

    if (selectedObjectGroups.length > 0) {
      filtered = filtered.filter(
        (recht) =>
          recht.objectGroup && selectedObjectGroups.includes(recht.objectGroup),
      )
    }

    if (selectedTypes.length > 0) {
      filtered = filtered.filter(
        (recht) => recht.typ && selectedTypes.includes(recht.typ),
      )
    }

    if (showOnlyGranted) {
      const grantedRefs = new Set([
        ...userRechte.map((r) => r.refAco),
        ...groupRechte.map((r) => r.refAco),
      ])
      filtered = filtered.filter((recht) => grantedRefs.has(recht.ref))
    }

    return filtered
  }, [
    rechte,
    searchQuery,
    selectedObjectGroups,
    selectedTypes,
    showOnlyGranted,
    userRechte,
    groupRechte,
  ])

  const groupedRechte = useMemo(() => {
    const groups = new Map<string, RECHTE_RESPONSE_DTO>()

    filteredRechte.forEach((recht) => {
      const group = recht.objectGroup || 'Unbekannt'
      if (!groups.has(group)) {
        groups.set(group, [])
      }
      groups.get(group)!.push(recht)
    })

    return groups
  }, [filteredRechte])

  const uniqueObjectGroups = useMemo(() => {
    return filteredRechte
      .map((recht) => recht.objectGroup)
      .filter((group): group is string => group !== undefined)
      .filter((group, index, array) => array.indexOf(group) === index)
      .sort()
  }, [filteredRechte])

  const uniqueTypes = useMemo(() => {
    return filteredRechte
      .map((recht) => recht.typ)
      .filter((type): type is string => type !== undefined)
      .filter((type, index, array) => array.indexOf(type) === index)
      .sort()
  }, [filteredRechte])

  const filterStats = useMemo(() => {
    const total = rechte.length
    const filtered = filteredRechte.length
    const grantedCount = filteredRechte.filter((recht) => {
      const grantedRefs = new Set([
        ...userRechte.map((r) => r.refAco),
        ...groupRechte.map((r) => r.refAco),
      ])
      return grantedRefs.has(recht.ref)
    }).length

    return {
      total,
      filtered,
      hidden: total - filtered,
      granted: grantedCount,
      denied: filtered - grantedCount,
      hasFilters:
        searchQuery.trim() !== '' ||
        selectedObjectGroups.length > 0 ||
        selectedTypes.length > 0 ||
        showOnlyGranted,
    }
  }, [
    rechte.length,
    filteredRechte,
    userRechte,
    groupRechte,
    searchQuery,
    selectedObjectGroups,
    selectedTypes,
    showOnlyGranted,
  ])

  return {
    filteredRechte,
    groupedRechte,
    uniqueObjectGroups,
    uniqueTypes,
    filterStats,
  }
}
