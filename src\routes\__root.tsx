import { Outlet, createRootRouteWithContext } from '@tanstack/react-router'
import TanStackQueryLayout from '../integrations/tanstack-query/layout.tsx'

import { Toaster } from '@/components/ui/sonner.tsx'
import { TooltipProvider } from '@/components/ui/tooltip.tsx'
import type { QueryClient } from '@tanstack/react-query'

interface MyRouterContext {
  queryClient: QueryClient
}

export const Route = createRootRouteWithContext<MyRouterContext>()({
  component: () => (
    <>
      <TooltipProvider>
        <Toaster position="top-center" />
        <div className="flex flex-col h-screen">
          <div className="flex-1 p-4">
            <Outlet />
          </div>
        </div>

        {import.meta.env.DEV && <TanStackQueryLayout />}
      </TooltipProvider>
    </>
  ),
})
