import createFetchClient from 'openapi-fetch'
import type { paths } from 'schema'
import type { LOGIN_REQUEST_BODY_TYPE, LOGIN_RESPONSE_TYPE } from './DTOs'

const loginClient = createFetchClient<paths>({
  baseUrl: `${import.meta.env.VITE_API_URL}`,
})

export const login = async (
  loginparams: LOGIN_REQUEST_BODY_TYPE,
): Promise<LOGIN_RESPONSE_TYPE> => {
  const { data, error } = await loginClient.POST('/api/login', {
    body: {
      password: loginparams.password,
      username: loginparams.username,
      schema: loginparams.schema,
      server: loginparams.server,
      userPrefix: loginparams.userPrefix,
    },
  })

  if (error || !data) {
    throw new Error(`Login fehlgeschlagen: ${error || 'Keine Daten erhalten'}`)
  }

  return data
}
