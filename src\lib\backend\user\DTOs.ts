import type { components, paths } from 'schema'

export type USER_RESPONSE_DTO =
  paths['/api/users']['get']['responses']['200']['content']['application/json']

export type UserItem = components['schemas']['SysBenView']

export type USER_RECHTE_RESPONSE_DTO =
  paths['/api/users/{userRef}/acos']['get']['responses']['200']['content']['application/json']

export type USER_GROUPS_RECHTE_RESPONSE_DTO =
  paths['/api/users/{userRef}/groups/acos']['get']['responses']['200']['content']['application/json']

export type CREATE_USER_REQUEST_DTO =
  paths['/api/users']['post']['requestBody']['content']['application/json']
