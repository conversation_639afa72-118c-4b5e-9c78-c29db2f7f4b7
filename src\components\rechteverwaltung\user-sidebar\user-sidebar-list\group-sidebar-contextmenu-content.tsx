import { ContextMenuContent } from '@/components/ui/context-menu'
import { CustomContextMenuItem } from '@/components/ui/context-menu-item'
import type { GROUP_RESPONSE_DTO } from '@/lib/backend/groups/DTOs'
import { Eye, Users } from 'lucide-react'
import type { GroupDialogMode } from '../group-dialog/group-dialog'

export const GroupSidebarContextMenuContent = ({
  group,
  onOpenDialog,
}: {
  group: GROUP_RESPONSE_DTO[number]
  onOpenDialog: (
    group: GROUP_RESPONSE_DTO[number],
    mode: GroupDialogMode,
  ) => void
}) => {
  return (
    <ContextMenuContent>
      <div className="flex flex-col items-center gap-2 p-2 border-b">
        <span className="font-semibold">{group.groupName}</span>
        <span className="text-xs text-gray-500">Gruppe</span>
      </div>
      <CustomContextMenuItem
        label="Details anzeigen"
        icon={<Eye />}
        onClick={() => {
          onOpenDialog(group, 'details')
        }}
      />
      <CustomContextMenuItem
        label="Benutzer anzeigen"
        icon={<Users />}
        onClick={() => {
          onOpenDialog(group, 'users')
        }}
      />
    </ContextMenuContent>
  )
}
