import { motion, useAnimationControls } from 'framer-motion'
import React, { useEffect, useState } from 'react'
import { Dot } from './dots/dots'
import type { LoadingLogoProps } from './loading-logo.types'

const LoadingLogo: React.FC<LoadingLogoProps> = ({ title }) => {
  const [completedAnimations, setCompletedAnimations] = useState(0)
  const controls = useAnimationControls()
  const [animationKey, setAnimationKey] = useState(0)

  useEffect(() => {
    if (completedAnimations === 3) {
      setCompletedAnimations(0)
      setAnimationKey((prevKey) => prevKey + 1)
    }
  }, [completedAnimations])

  useEffect(() => {
    controls.start('visible')
  }, [controls, animationKey])

  const handleAnimationComplete = () => {
    setCompletedAnimations((prev) => prev + 1)
  }

  return (
    <motion.div
      layout
      key={animationKey}
      className="flex flex-col items-center justify-center space-y-4"
    >
      <svg
        width="40"
        height="40"
        viewBox="0 0 22 22"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <Dot
          custom={1}
          onCompleted={handleAnimationComplete}
          controls={controls}
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M1.74118 3.49236C2.70256 3.49176 3.48194 2.70963 3.48194 1.74618C3.48313 0.78213 2.70256 0.000598416 1.74118 0C0.780385 0.000598416 -0.000183105 0.782728 -0.000183105 1.74618C-0.000183105 2.70963 0.780385 3.49176 1.74118 3.49236Z"
            fill="url(#paint0_linear_1)"
          />
        </Dot>
        <Dot
          custom={2}
          onCompleted={handleAnimationComplete}
          controls={controls}
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M9.86338 6.40025C9.86338 8.32775 8.30582 9.89201 6.38305 9.89201C4.45967 9.89201 2.90033 8.32715 2.90033 6.40085C2.90033 4.47215 4.45967 2.90969 6.38185 2.90909C8.30582 2.90969 9.86338 4.47095 9.86338 6.40025Z"
            fill="url(#paint0_linear_2)"
          />
        </Dot>
        <Dot
          custom={3}
          onCompleted={handleAnimationComplete}
          controls={controls}
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M21.6786 14.7576C21.6786 10.9008 18.5617 7.7753 14.7167 7.77589C10.8712 7.7753 7.7525 10.9008 7.7525 14.7576C7.7525 18.612 10.8712 21.7399 14.7156 21.7399C18.5617 21.7399 21.6786 18.6114 21.6786 14.7576Z"
            fill="url(#paint0_linear_3)"
          />

          <line
            x1="12.1802"
            y1="12.1802"
            x2="17.8295"
            y2="17.8295"
            stroke="white"
            strokeWidth="2"
            strokeLinecap="round"
          />
          <line
            x1="17.8295"
            y1="12.1802"
            x2="12.1802"
            y2="17.8295"
            stroke="white"
            strokeWidth="2"
            strokeLinecap="round"
          />
        </Dot>
        <defs>
          <linearGradient
            id="paint0_linear_1"
            x1="10.8392"
            y1="0"
            x2="10.8392"
            y2="21.7399"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#57DD00" />
            <stop offset="1" stopColor="#CAE114" />
          </linearGradient>
          <linearGradient
            id="paint0_linear_2"
            x1="10.8392"
            y1="0"
            x2="10.8392"
            y2="21.7399"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#57DD00" />
            <stop offset="1" stopColor="#CAE114" />
          </linearGradient>
          <linearGradient
            id="paint0_linear_3"
            x1="10.8392"
            y1="0"
            x2="10.8392"
            y2="21.7399"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#57DD00" />
            <stop offset="1" stopColor="#CAE114" />
          </linearGradient>
        </defs>
      </svg>
      <h1 className="font-bold">{title}</h1>
    </motion.div>
  )
}

export default LoadingLogo
