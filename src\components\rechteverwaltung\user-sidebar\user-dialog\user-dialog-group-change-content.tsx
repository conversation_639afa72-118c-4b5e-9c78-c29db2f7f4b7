import { Button } from '@/components/ui/button'
import { useGroups, useUserGroups } from '@/lib/backend/groups/hooks'
import {
  DndContext,
  type DragEndEvent,
  type DragOverEvent,
  DragOverlay,
  type DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  type UniqueIdentifier,
  closestCenter,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import { sortableKeyboardCoordinates } from '@dnd-kit/sortable'
import { ArrowLeft, ArrowRight, UserPlus, Users } from 'lucide-react'
import { useCallback, useMemo, useState } from 'react'
import { createPortal } from 'react-dom'
import { DraggableGroupCard } from './user-dialog-draggable-group-card'
import { GroupContainer } from './user-dialog-group-container'

type UserDialogGroupChangeContentProps = {
  userRef: number
}

export const UserDialogGroupChangeContent: React.FC<
  UserDialogGroupChangeContentProps
> = ({ userRef }) => {
  const { data: allGroups } = useGroups()
  const { data: userGroups } = useUserGroups(userRef)

  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null)
  const [isDragOver, setIsDragOver] = useState<{
    container: string | null
    isOver: boolean
  }>({ container: null, isOver: false })

  const [selectedAvailable, setSelectedAvailable] = useState<Set<number>>(
    new Set(),
  )
  const [selectedAssigned, setSelectedAssigned] = useState<Set<number>>(
    new Set(),
  )

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  const { availableGroups, assignedGroups } = useMemo(() => {
    if (!allGroups || !userGroups) {
      return { availableGroups: [], assignedGroups: [] }
    }

    const assignedGroupRefs = new Set(userGroups.map((group) => group.ref))

    return {
      availableGroups: allGroups.filter(
        (group) => !assignedGroupRefs.has(group.ref),
      ),
      assignedGroups: userGroups,
    }
  }, [allGroups, userGroups])

  const activeGroup = useMemo(() => {
    if (!activeId) return null
    return [...availableGroups, ...assignedGroups].find(
      (group) => group.ref === activeId,
    )
  }, [activeId, availableGroups, assignedGroups])

  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id)
  }, [])

  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { over } = event

    if (over && (over.id === 'available' || over.id === 'assigned')) {
      setIsDragOver({
        container: over.id as string,
        isOver: true,
      })
    } else {
      setIsDragOver({ container: null, isOver: false })
    }
  }, [])

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event

      setActiveId(null)
      setIsDragOver({ container: null, isOver: false })

      if (!over || !active) return

      const draggedGroupRef = active.id as number
      const targetContainer = over.id as string

      const isCurrentlyInAvailable = availableGroups.some(
        (g) => g.ref === draggedGroupRef,
      )
      const isCurrentlyInAssigned = assignedGroups.some(
        (g) => g.ref === draggedGroupRef,
      )

      if (isCurrentlyInAvailable && targetContainer === 'assigned') {
        handleGroupAssignment(draggedGroupRef, 'assign')
      } else if (isCurrentlyInAssigned && targetContainer === 'available') {
        handleGroupAssignment(draggedGroupRef, 'remove')
      }
    },
    [availableGroups, assignedGroups],
  )

  const handleGroupAssignment = useCallback(
    (groupRef: number, action: 'assign' | 'remove') => {
      const group = [...availableGroups, ...assignedGroups].find(
        (g) => g.ref === groupRef,
      )

      if (!group) return

      // TODO: Hier würde später die echte API-Mutation aufgerufen werden
      // Beispiel:
      // if (action === 'assign') {
      //   await assignGroupToUser.mutateAsync({ userRef, groupRef })
      // } else {
      //   await removeGroupFromUser.mutateAsync({ userRef, groupRef })
      // }
    },
    [availableGroups, assignedGroups, userRef],
  )

  const handleBulkAssign = useCallback(() => {
    selectedAvailable.forEach((groupRef) => {
      handleGroupAssignment(groupRef, 'assign')
    })
    setSelectedAvailable(new Set())
  }, [selectedAvailable, handleGroupAssignment])

  const handleBulkRemove = useCallback(() => {
    selectedAssigned.forEach((groupRef) => {
      handleGroupAssignment(groupRef, 'remove')
    })
    setSelectedAssigned(new Set())
  }, [selectedAssigned, handleGroupAssignment])

  const toggleAvailableSelection = useCallback((groupRef: number) => {
    setSelectedAvailable((prev) => {
      const newSet = new Set(prev)
      if (newSet.has(groupRef)) {
        newSet.delete(groupRef)
      } else {
        newSet.add(groupRef)
      }
      return newSet
    })
  }, [])

  const toggleAssignedSelection = useCallback((groupRef: number) => {
    setSelectedAssigned((prev) => {
      const newSet = new Set(prev)
      if (newSet.has(groupRef)) {
        newSet.delete(groupRef)
      } else {
        newSet.add(groupRef)
      }
      return newSet
    })
  }, [])

  const selectAllAvailable = useCallback(() => {
    setSelectedAvailable(
      new Set(
        availableGroups
          .filter((g) => g.ref !== undefined)
          .map((g) => g.ref as number),
      ),
    )
  }, [availableGroups])

  const selectAllAssigned = useCallback(() => {
    setSelectedAssigned(
      new Set(
        assignedGroups
          .filter((g) => g.ref !== undefined)
          .map((g) => g.ref as number),
      ),
    )
  }, [assignedGroups])

  const clearAvailableSelection = useCallback(() => {
    setSelectedAvailable(new Set())
  }, [])

  const clearAssignedSelection = useCallback(() => {
    setSelectedAssigned(new Set())
  }, [])

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
      accessibility={{
        restoreFocus: true,
      }}
    >
      <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <GroupContainer
            id="available"
            title="Verfügbare Gruppen"
            icon={<Users className="w-5 h-5" />}
            groups={availableGroups}
            isDropTarget={isDragOver.container === 'available'}
            isEmpty={availableGroups.length === 0}
            selectedGroups={selectedAvailable}
            onToggleSelection={toggleAvailableSelection}
            onSelectAll={selectAllAvailable}
            onClearSelection={clearAvailableSelection}
          />
          <GroupContainer
            id="assigned"
            title="Zugewiesene Gruppen"
            icon={<UserPlus className="w-5 h-5" />}
            groups={assignedGroups}
            isDropTarget={isDragOver.container === 'assigned'}
            isEmpty={assignedGroups.length === 0}
            selectedGroups={selectedAssigned}
            onToggleSelection={toggleAssignedSelection}
            onSelectAll={selectAllAssigned}
            onClearSelection={clearAssignedSelection}
          />
        </div>
        <div className="flex items-center justify-center gap-4 py-6">
          <Button
            variant="outline"
            size="sm"
            onClick={handleBulkAssign}
            disabled={selectedAvailable.size === 0}
            className="flex items-center gap-2"
          >
            <ArrowRight className="w-4 h-4" />
            Zuweisen ({selectedAvailable.size})
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleBulkRemove}
            disabled={selectedAssigned.size === 0}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Entfernen ({selectedAssigned.size})
          </Button>
        </div>
      </div>

      {createPortal(
        <DragOverlay>
          {activeGroup ? (
            <DraggableGroupCard
              group={activeGroup}
              isDragOverlay
              isSelected={false}
              onToggleSelection={() => {}}
            />
          ) : null}
        </DragOverlay>,
        document.body,
      )}
    </DndContext>
  )
}
