import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useFilterStore } from '@/lib/stores/useFilterStore'
import { Filter, RotateCcw, Search, X } from 'lucide-react'
import { ObjectGroupSelect } from './object-group-select'
import { TypeSelect } from './type-select'

export const FilterHeader = () => {
  const {
    searchQuery,
    setSearchQuery,
    showOnlyGranted,
    setShowOnlyGranted,
    clearFilters,
    selectedObjectGroups,
    selectedTypes,
  } = useFilterStore()

  const hasActiveFilters =
    searchQuery ||
    showOnlyGranted ||
    selectedObjectGroups.length > 0 ||
    selectedTypes.length > 0

  return (
    <div className="space-y-4 p-4 sm:p-6 bg-background/50 backdrop-blur-sm rounded-2xl border border-border/50">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={`p-2 rounded-lg bg-primary-gradient`}>
            <Filter className="h-4 w-4 text-black" />
          </div>
          <h3 className="text-lg font-semibold text-foreground">Filter</h3>
          {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
          >
            <RotateCcw className="w-4 h-4" />
            <span className="hidden sm:inline">Filter zurücksetzen</span>
            <span className="sm:hidden">Zurücksetzen</span>
          </Button>
        )}
        </div>

        <div className="flex items-end pb-2 md:col-span-2 lg:col-span-1 xl:col-span-1 2xl:col-span-1">
          <div className="flex items-center gap-2">
            <Checkbox
              id="nur-gewaehrte"
              checked={showOnlyGranted}
              onCheckedChange={(checked) => setShowOnlyGranted(!!checked)}
            />
            <Label
              htmlFor="nur-gewaehrte"
              className="text-sm font-medium cursor-pointer whitespace-nowrap"
            >
              Nur gewährte Rechte
            </Label>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-3 lg:gap-4">
        <div className="space-y-2 md:col-span-1 lg:col-span-2 xl:col-span-2 2xl:col-span-1">
          <Label
            htmlFor="rechte-suche"
            className="text-sm font-medium flex items-center gap-2"
          >
            <Search className="w-4 h-4" />
            Suche
          </Label>
          <div className="relative">
            <Input
              id="rechte-suche"
              placeholder="Rechte durchsuchen..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSearchQuery('')}
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              >
                <X className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium">Objektgruppe</Label>
          <ObjectGroupSelect />
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium">Typ</Label>
          <TypeSelect />
        </div>
      </div>
    </div>
  )
}
