import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import type { UserItem } from '@/lib/backend/user/DTOs'
import {
  AtSign,
  Badge,
  Building2,
  KeyRound,
  Languages,
  Mail,
  ShieldCheck,
  User2,
} from 'lucide-react'
import { UserDialogFirmaSelect } from './user-dialog-firma-select'
import { UserDialogSpracheSelect } from './user-dialog-sprache-select'

interface UserDialogUserContentProps {
  user: UserItem
  onClose: () => void
}

export const UserDialogUserContent = ({ user }: UserDialogUserContentProps) => {
  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-8">
      <div className="relative">
        <div className="flex items-center gap-6 pb-6 border-b border-border/50">
          <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center ring-1 ring-border/20">
            <User2 className="w-8 h-8 text-primary" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-2xl font-semibold text-foreground tracking-tight">
                {user.userName || (
                  <span className="text-muted-foreground">Kein Name</span>
                )}
              </h1>
              <span
                className={`px-3 py-1 rounded-full text-xs font-medium ring-1 ${
                  user.status === 'AKT'
                    ? 'bg-emerald-50 text-emerald-700 ring-emerald-200/50'
                    : 'bg-gray-50 text-gray-600 ring-gray-200/50'
                }`}
              >
                {user.status || 'Unbekannt'}
              </span>
            </div>
            <div className="flex items-center gap-2 text-muted-foreground">
              <Mail className="w-4 h-4" />
              <span className="text-sm">
                {user.email || (
                  <span className="text-muted-foreground/60">Keine E-Mail</span>
                )}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          <div>
            <div className="flex items-center gap-2 mb-4">
              <Badge className="w-5 h-5 text-primary" />
              <h2 className="text-lg font-medium text-foreground">
                Benutzer-Details
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="group">
                <div className="flex items-center gap-2 mb-2">
                  <Badge className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm font-medium text-muted-foreground">
                    Titel
                  </span>
                </div>
                <p className="text-base text-foreground font-medium pl-6">
                  {user.userTitle || (
                    <span className="text-muted-foreground/60">Kein Titel</span>
                  )}
                </p>
              </div>

              <div className="group">
                <div className="flex items-center gap-2 mb-2">
                  <KeyRound className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm font-medium text-muted-foreground">
                    Benutzer-ID
                  </span>
                </div>
                <p className="text-base text-foreground font-mono font-medium pl-6">
                  {user.userId || (
                    <span className="text-muted-foreground/60">Keine ID</span>
                  )}
                </p>
              </div>

              <div className="group">
                <div className="flex items-center gap-2 mb-2">
                  <AtSign className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm font-medium text-muted-foreground">
                    Kürzel
                  </span>
                </div>
                <p className="text-base text-foreground font-mono font-medium pl-6">
                  {user.userShortName || (
                    <span className="text-muted-foreground/60">
                      Kein Kürzel
                    </span>
                  )}
                </p>
              </div>
            </div>
          </div>

          <div className="pt-6 border-t border-border/30">
            <div className="flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-primary/5 to-primary/10 border border-primary/10">
              <Checkbox
                checked={!!user.isAdmin}
                disabled
                id="admin-checkbox"
                className="flex-shrink-0"
              />
              <Label
                htmlFor="admin-checkbox"
                className="cursor-pointer flex items-center gap-3 flex-1"
              >
                <ShieldCheck className="w-5 h-5 text-primary" />
                <span className="text-base font-medium text-foreground">
                  Administrator-Berechtigung
                </span>
              </Label>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <div className="flex items-center gap-2 mb-4">
              <Languages className="w-5 h-5 text-primary" />
              <h2 className="text-lg font-medium text-foreground">
                Einstellungen
              </h2>
            </div>

            <div className="space-y-5">
              <div>
                <Label className="text-sm font-medium text-muted-foreground flex items-center gap-2 mb-3">
                  <Languages className="w-4 h-4" />
                  Sprache
                </Label>
                <UserDialogSpracheSelect defaultValue={user.sprache} />
              </div>

              <div>
                <Label className="text-sm font-medium text-muted-foreground flex items-center gap-2 mb-3">
                  <Building2 className="w-4 h-4" />
                  Firma
                </Label>
                <UserDialogFirmaSelect defaultValue={user.refFirma} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
