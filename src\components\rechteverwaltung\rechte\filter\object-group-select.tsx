import { Combobox } from '@/components/ui/combobox'
import { useObjectGroups } from '@/lib/backend/objectgroups/hooks'
import { useFilterStore } from '@/lib/stores/useFilterStore'
import { GROUP_NAMES } from '../tree-view/constants'

export const ObjectGroupSelect = () => {
  const { data: objectGroups } = useObjectGroups()
  const { selectedObjectGroups, setSelectedObjectGroups } = useFilterStore()

  const handleSelect = (value: string | string[]) => {
    if (Array.isArray(value)) {
      setSelectedObjectGroups(value)
    } else {
      setSelectedObjectGroups([value])
    }
  }

  const items =
    objectGroups?.map((group) => ({
      value: group,
      label: GROUP_NAMES[group] || group,
    })) || []

  return (
    <Combobox
      items={items}
      placeholder="Wähle eine Gruppe"
      multipleSelection
      value={selectedObjectGroups}
      setValue={handleSelect}
    />
  )
}
