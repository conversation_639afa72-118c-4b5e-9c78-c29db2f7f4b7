import { Badge } from '@/components/ui/badge'
import { ContextMenu, ContextMenuTrigger } from '@/components/ui/context-menu'
import type { UserItem } from '@/lib/backend/user/DTOs'
import { ACTIVE_USER_COLOR } from '@/lib/constants'
import { useActiveUser } from '@/lib/stores/useActiveUser'
import { cn } from '@/lib/utils'
import type { UserDialogMode } from '../user-dialog/user-dialog'
import { UserSidebarContextMenuContent } from './user-sidebar-contextmenu-content'

interface UserSidebarItemProps extends UserItem {
  onOpenDialog?: (user: UserItem, mode: UserDialogMode) => void
  isContextMenuDisabled?: boolean
  onSelect?: (user: UserItem) => void
}

export function UserSidebarItem({
  isContextMenuDisabled = false,
  onOpenDialog,
  onSelect,
  ...user
}: UserSidebarItemProps) {
  const { activeUserRef, setActiveUserRef } = useActiveUser()

  const isActive = activeUserRef === user.ref

  return (
    <ContextMenu>
      <ContextMenuTrigger disabled={isContextMenuDisabled}>
        <div
          className={cn(
            'flex items-center gap-3 p-2  rounded-2xl transition-colors cursor-pointer hover:bg-gray-50',
            isActive
              ? `${ACTIVE_USER_COLOR}  text-gray-900`
              : 'text-gray-700 hover:text-gray-900',
          )}
          onClick={() => {
            if (onSelect) {
              onSelect(user)
            } else {
              setActiveUserRef(user.ref!)
            }
          }}
        >
          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold text-lg">
            {user.userName?.[0]?.toUpperCase() || '?'}
          </div>
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {user.userName || 'Kein Nutzername'}
            </span>
            <span className="text-xs text-gray-500">{user.firma}</span>
            <span className="text-xs text-gray-400">{user.userId}</span>
          </div>
          {user.isAdmin && (
            <Badge variant="destructive" className="text-xs">
              Admin
            </Badge>
          )}
          {user.status === 'AKT' && (
            <span className="ml-auto px-2 py-0.5 rounded-full bg-green-200 text-green-800 text-xs font-semibold">
              Aktiv
            </span>
          )}
        </div>
      </ContextMenuTrigger>
      <UserSidebarContextMenuContent
        user={user}
        onOpenDialog={onOpenDialog || (() => {})}
      />
    </ContextMenu>
  )
}
