'use client'

import { CheckIcon, ChevronsUpDownIcon } from 'lucide-react'
import * as React from 'react'

import { Button } from '@/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { cn } from '@/lib/utils'

export function Combobox({
  items,
  placeholder = 'Select an option',
  multipleSelection = false,
  value,
  setValue,
}: {
  items: {
    value: string
    label: string
  }[]
  placeholder?: string
  multipleSelection?: boolean
  value: string | string[]
  setValue: (value: string | string[]) => void
}) {
  const [open, setOpen] = React.useState(false)

  const handleSelect = (currentValue: string) => {
    if (multipleSelection && Array.isArray(value)) {
      const newValue = value.includes(currentValue)
        ? value.filter((v) => v !== currentValue)
        : [...value, currentValue]
      setValue(newValue)
    } else {
      setValue(currentValue === value ? '' : currentValue)
      setOpen(false)
    }
  }

  const getDisplayValue = () => {
    if (multipleSelection && Array.isArray(value)) {
      if (value.length === 0) return placeholder
      if (value.length === 1) {
        return (
          items.find((item) => item.value === value[0])?.label || placeholder
        )
      }
      return `${value.length} ausgewählt`
    } else {
      return typeof value === 'string' && value
        ? items.find((item) => item.value === value)?.label
        : placeholder
    }
  }

  const isSelected = (itemValue: string) => {
    if (multipleSelection && Array.isArray(value)) {
      return value.includes(itemValue)
    } else {
      return value === itemValue
    }
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-[200px] justify-between"
        >
          {getDisplayValue()}
          <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder={placeholder} />
          <CommandList>
            <CommandEmpty>Keine Einträge gefunden.</CommandEmpty>
            <CommandGroup>
              {items.map((item) => (
                <CommandItem
                  key={item.value}
                  value={item.value}
                  onSelect={() => handleSelect(item.value)}
                >
                  <CheckIcon
                    className={cn(
                      'mr-2 h-4 w-4',
                      isSelected(item.value) ? 'opacity-100' : 'opacity-0',
                    )}
                    color="#57dd00"
                  />
                  {item.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
