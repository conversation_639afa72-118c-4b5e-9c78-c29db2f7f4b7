import { ContextMenu, ContextMenuTrigger } from '@/components/ui/context-menu'
import type { GROUP_RESPONSE_DTO } from '@/lib/backend/groups/DTOs'
import { ACTIVE_USER_COLOR } from '@/lib/constants'
import { useActiveUser } from '@/lib/stores/useActiveUser'
import { cn } from '@/lib/utils'
import type { GroupDialogMode } from '../group-dialog/group-dialog'
import { GroupSidebarContextMenuContent } from './group-sidebar-contextmenu-content'

export function GroupSidebarItem(
  group: GROUP_RESPONSE_DTO[number] & {
    onOpenDialog: (
      group: GROUP_RESPONSE_DTO[number],
      mode: GroupDialogMode,
    ) => void
  },
) {
  const { activeGroupRef, setActiveGroupRef } = useActiveUser()

  const isActive = activeGroupRef === group.ref

  return (
    <ContextMenu>
      <ContextMenuTrigger>
        <div
          className={cn(
            'flex items-center gap-3 p-2  rounded-2xl transition-colors cursor-pointer hover:bg-gray-50',
            isActive
              ? `${ACTIVE_USER_COLOR}  text-gray-900`
              : 'text-gray-700 hover:text-gray-900',
          )}
          onClick={() => {
            setActiveGroupRef(group.ref!)
          }}
        >
          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-bold text-lg">
            {group.groupName?.[0]?.toUpperCase() || '?'}
          </div>
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {group.groupName || 'Kein Gruppenname'}
            </span>
            <span className="text-xs text-gray-500">{group.groupArt}</span>
            <span className="text-xs text-gray-400">{group.groupId}</span>
          </div>
        </div>
      </ContextMenuTrigger>
      <GroupSidebarContextMenuContent
        group={group}
        onOpenDialog={group.onOpenDialog}
      />
    </ContextMenu>
  )
}
