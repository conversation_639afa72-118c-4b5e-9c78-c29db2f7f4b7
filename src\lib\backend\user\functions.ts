import { toast } from 'sonner'
import { fetchClient } from '../client'
import type { CREATE_USER_REQUEST_DTO, UPDATE_USER_REQUEST_DTO } from './DTOs'

export async function createUser(newUser: CREATE_USER_REQUEST_DTO) {
  const response = await fetchClient.POST('/api/users', {
    body: newUser,
  })

  if (response.error) {
    throw new Error('<PERSON>hler beim Erstellen des Benutzers')
  }

  toast('Neuer Benutzer erstellt.')
  return response.data
}

export async function checkUserExists(user_ID: string) {
  const response = await fetchClient.GET('/api/users/exists/{userId}', {
    params: {
      path: {
        userId: user_ID,
      },
    },
  })

  if (response.error) {
    throw new Error('Fehler beim Erstellen des Benutzers')
  }
  return response.data
}

export async function updateUser(updateUser: UPDATE_USER_REQUEST_DTO) {
  const response = await fetchClient.PUT('/api/users', {
    body: updateUser,
  })

  if (response.error) {
    throw new Error('Fehler beim Aktualisieren des Benutzers')
  }

  toast('Benutzer erfolgreich aktualisiert.')
  return response.data
}
