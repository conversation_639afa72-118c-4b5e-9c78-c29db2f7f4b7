import { toast } from 'sonner'
import { fetchClient } from '../client'
import type { CREATE_USER_REQUEST_DTO } from './DTOs'

export async function createUser(newUser: CREATE_USER_REQUEST_DTO) {
  const response = await fetchClient.POST('/api/users', {
    body: newUser,
  })

  if (response.error) {
    throw new Error('Fehler beim Erstellen des Benutzers')
  }

  toast('Neuer Benutzer erstellt.')
  return response.data
}

export async function checkUserExists(user_ID: string) {
  const response = await fetchClient.GET('/api/users/exists/{userId}', {
    params: {
      path: {
        userId: user_ID,
      },
    },
  })

  if (response.error) {
    throw new Error('Fehler beim Erstellen des Benutzers')
  }
  return response.data
}
