import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON>itle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { ArrowLeft } from 'lucide-react'
import { useState } from 'react'
import { AddDialogCreateGroupContent } from './add-dialog-create-group'
import { UserDialogCreateUserContent } from './add-dialog-create-user'
import { AddDialogSelect } from './add-dialog-select'

type AddDialogFlowProps = {
  trigger: React.ReactNode
}

type FlowStep = 'select' | 'create-user' | 'create-group'

export const AddDialogFlow = ({ trigger }: AddDialogFlowProps) => {
  const [open, setOpen] = useState(false)
  const [currentStep, setCurrentStep] = useState<FlowStep>('select')

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen)
    if (!newOpen) {
      setCurrentStep('select')
    }
  }

  const handleBack = () => {
    setCurrentStep('select')
  }

  const handleSelectUser = () => {
    setCurrentStep('create-user')
  }

  const handleSelectGroup = () => {
    setCurrentStep('create-group')
  }

  const getDialogTitle = () => {
    switch (currentStep) {
      case 'select':
        return 'Hinzufügen'
      case 'create-user':
        return 'Neuen Benutzer erstellen'
      case 'create-group':
        return 'Neue Gruppe erstellen'
      default:
        return 'Hinzufügen'
    }
  }

  const showBackButton = currentStep !== 'select'

  const renderContent = () => {
    switch (currentStep) {
      case 'select':
        return (
          <AddDialogSelect
            onSelectUser={handleSelectUser}
            onSelectGroup={handleSelectGroup}
          />
        )
      case 'create-user':
        return <UserDialogCreateUserContent />
      case 'create-group':
        return <AddDialogCreateGroupContent />
      default:
        return null
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="min-w-4xl">
        <DialogHeader className="relative">
          <div className="flex items-center gap-4">
            {showBackButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="flex-shrink-0"
              >
                <ArrowLeft className="w-4 h-4" />
              </Button>
            )}
            <DialogTitle className="text-left">{getDialogTitle()}</DialogTitle>
          </div>
        </DialogHeader>

        <div className="mt-6">{renderContent()}</div>
      </DialogContent>
    </Dialog>
  )
}
