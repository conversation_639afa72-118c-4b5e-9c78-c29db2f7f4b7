import { create } from 'zustand'

type ActiveUserStore = {
  activeUserRef: number | null
  setActiveUserRef: (ref: number | null) => void
  activeGroupRef: number | null
  setActiveGroupRef: (ref: number | null) => void
}

export const useActiveUser = create<ActiveUserStore>((set) => ({
  activeUserRef: null,
  setActiveUserRef: (ref) => set({ activeUserRef: ref, activeGroupRef: null }),
  activeGroupRef: null,
  setActiveGroupRef: (ref) => set({ activeGroupRef: ref, activeUserRef: null }),
}))
