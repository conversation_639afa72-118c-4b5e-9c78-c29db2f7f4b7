import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import type { RECHTE_RESPONSE_DTO } from '@/lib/backend/rechte/DTOs'
import type { USER_GROUPS_RECHTE_RESPONSE_DTO } from '@/lib/backend/user/DTOs'
import { TreeNodeItem } from './tree-view-node-item'
import type { PermissionData } from './useData'

type TreeNodeProps = {
  permission: RECHTE_RESPONSE_DTO[number] & { children?: RECHTE_RESPONSE_DTO }
  userPermissions: PermissionData
  groupPermissions: USER_GROUPS_RECHTE_RESPONSE_DTO
  onPermissionChange: (permission: RECHTE_RESPONSE_DTO[number]) => void
  depth?: number
}

export const TreeNode = ({
  permission,
  userPermissions,
  groupPermissions,
  onPermissionChange,
  depth = 0,
}: TreeNodeProps) => {
  const hasChildren = permission.children && permission.children.length > 0
  const paddingLeft = depth * 24

  if (hasChildren) {
    return (
      <Collapsible className="space-y-1">
        <CollapsibleTrigger
          className="group flex items-center  gap-3 w-full text-left p-3 rounded-lg hover:bg-slate-50"
          style={{ paddingLeft: `${paddingLeft}px` }}
        >
          <TreeNodeItem
            permission={permission}
            userPermissions={userPermissions}
            groupPermissions={groupPermissions}
            onPermissionChange={onPermissionChange}
            showChevron={true}
            hasChildren={hasChildren}
            isExpanded={false}
          />
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-1">
          {permission.children?.map((child) => (
            <TreeNode
              key={child.ref}
              permission={child}
              userPermissions={userPermissions}
              groupPermissions={groupPermissions}
              onPermissionChange={onPermissionChange}
              depth={depth + 1}
            />
          ))}
        </CollapsibleContent>
      </Collapsible>
    )
  }

  return (
    <div
      className="flex items-center gap-3 p-3 rounded-lg hover:bg-slate-50 "
      style={{ paddingLeft: `${paddingLeft}px` }}
    >
      <TreeNodeItem
        permission={permission}
        userPermissions={userPermissions}
        groupPermissions={groupPermissions}
        onPermissionChange={onPermissionChange}
      />
    </div>
  )
}
