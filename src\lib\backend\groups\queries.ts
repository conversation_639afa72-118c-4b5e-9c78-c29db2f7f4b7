import { $api } from '../client'

export const groupsQueryOptions = $api.queryOptions('get', '/api/groups', {
  queryKey: ['groups'],
})

export const userqroupsQueryOptions = (userRef: number) =>
  $api.queryOptions('get', '/api/users/{userRef}/groups', {
    queryKey: ['groups', userRef],
    params: {
      path: {
        userRef,
      },
    },
  })

export const groupUsersQueryOptions = (groupREF: number) =>
  $api.queryOptions('get', '/api/groups/{groupRef}/users', {
    queryKey: ['groups-users', groupREF],
    params: {
      path: {
        groupRef: groupREF,
      },
    },
  })

  export const groupACOsQueryOptions = (groupREF: number) =>
    $api.queryOptions('get', '/api/groups/{groupRef}/acos', {
      queryKey: ['groups-rechte', groupREF],
      params: {
        path: {
          groupRef: groupREF,
        },
      },
    })
