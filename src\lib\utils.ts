import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getCookieValue(name: string): string | undefined {
  const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'))
  if (match && match[2]) return decodeURIComponent(match[2])
  return undefined
}

export function setCookieValue(
  name: string,
  value: string,
  days?: number,
): void {
  let expires = ''
  if (days) {
    const date = new Date()
    date.setTime(date.getTime() + days * 86400000)
    expires = '; expires=' + date.toUTCString()
  }
  document.cookie =
    name + '=' + encodeURIComponent(value) + expires + '; path=/'
}
