import type { RECHTE_RESPONSE_DTO } from '@/lib/backend/rechte/DTOs'
import type { USER_GROUPS_RECHTE_RESPONSE_DTO } from '@/lib/backend/user/DTOs'
import { TreeNode } from './tree-view-node'
import type { PermissionData } from './useData'
import { buildHierarchy } from './utils'

type TreeViewProps = {
  all_permissions: RECHTE_RESPONSE_DTO
  user_permissions: PermissionData
  group_permissions: USER_GROUPS_RECHTE_RESPONSE_DTO
  onPermissionChange: (permission: RECHTE_RESPONSE_DTO[number]) => void
}

export const TreeView = ({
  all_permissions: permissions,
  user_permissions,
  group_permissions,
  onPermissionChange,
}: TreeViewProps) => {
  const hierarchicalPermissions = buildHierarchy(permissions)

  if (!user_permissions) {
    return null
  }

  return (
    <div className="space-y-2">
      {permissions.map((permission) => (
        <TreeNode
          key={permission.ref}
          permission={permission}
          userPermissions={user_permissions}
          groupPermissions={group_permissions}
          onPermissionChange={onPermissionChange}
          depth={0}
        />
      ))}
    </div>
  )
}
