import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import type { RECHTE_RESPONSE_DTO } from '@/lib/backend/rechte/DTOs'
import {
  Box,
  Building2,
  FileText,
  Hash,
  Key,
  ShieldCheck,
  Tag,
} from 'lucide-react'
import { getTypeBadgeColor } from '../tree-view/utils'

export const DetailsDialog = ({
  open,
  onOpenChange,
  permission,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
  permission: RECHTE_RESPONSE_DTO[number]
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Berechtigungs-Details
          </DialogTitle>
          <DialogDescription>
            Detaillierte Informationen zu dieser Berechtigung
          </DialogDescription>
        </DialogHeader>

        <div className="w-full max-w-4xl mx-auto space-y-8">
          <div className="relative">
            <div className="flex items-center gap-6 pb-6 border-b border-border/50">
              <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center ring-1 ring-border/20">
                <ShieldCheck className="w-8 h-8 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-2xl font-semibold text-foreground tracking-tight">
                    {permission.beschreibung || (
                      <span className="text-muted-foreground">
                        Keine Beschreibung
                      </span>
                    )}
                  </h1>
                  {permission.typ && (
                    <Badge className={getTypeBadgeColor(permission.typ)}>
                      {permission.typ}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Key className="w-4 h-4" />
                  <span className="text-sm font-mono">
                    {permission.name || (
                      <span className="text-muted-foreground/60">
                        Kein technischer Name
                      </span>
                    )}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div>
                <div className="space-y-4">
                  <div className="group">
                    <div className="flex items-center gap-2 mb-2">
                      <FileText className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">
                        Technischer Name
                      </span>
                    </div>
                    <p className="text-base text-foreground font-mono font-medium pl-6">
                      {permission.name || (
                        <span className="text-muted-foreground/60">
                          Nicht verfügbar
                        </span>
                      )}
                    </p>
                  </div>

                  <div className="group">
                    <div className="flex items-center gap-2 mb-2">
                      <Tag className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">
                        Typ
                      </span>
                    </div>
                    <div className="pl-6">
                      {permission.typ ? (
                        <Badge className={getTypeBadgeColor(permission.typ)}>
                          {permission.typ}
                        </Badge>
                      ) : (
                        <span className="text-base text-muted-foreground/60">
                          Nicht verfügbar
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="group">
                    <div className="flex items-center gap-2 mb-2">
                      <Building2 className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">
                        Anwendung
                      </span>
                    </div>
                    <p className="text-base text-foreground font-medium pl-6">
                      {permission.application || (
                        <span className="text-muted-foreground/60">
                          Nicht verfügbar
                        </span>
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <div>
                <div className="space-y-4">
                  <div className="group">
                    <div className="flex items-center gap-2 mb-2">
                      <Box className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">
                        Objektgruppe
                      </span>
                    </div>
                    <p className="text-base text-foreground font-medium pl-6">
                      {permission.objectGroup || (
                        <span className="text-muted-foreground/60">
                          Nicht verfügbar
                        </span>
                      )}
                    </p>
                  </div>

                  <div className="group">
                    <div className="flex items-center gap-2 mb-2">
                      <FileText className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">
                        Objektname
                      </span>
                    </div>
                    <p className="text-base text-foreground font-medium pl-6 text-ellipsis overflow-hidden">
                      {permission.objectName || (
                        <span className="text-muted-foreground/60">
                          Nicht verfügbar
                        </span>
                      )}
                    </p>
                  </div>

                  <div className="group">
                    <div className="flex items-center gap-2 mb-2">
                      <Hash className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm font-medium text-muted-foreground">
                        Objekt-ID
                      </span>
                    </div>
                    <p className="text-base text-foreground font-mono font-medium pl-6">
                      {permission.objectId !== null &&
                      permission.objectId !== undefined ? (
                        permission.objectId
                      ) : (
                        <span className="text-muted-foreground/60">
                          Nicht verfügbar
                        </span>
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {permission.beschreibung && (
            <div className="pt-6 border-t border-border/30">
              <div className="p-4 rounded-xl bg-gradient-to-r from-primary/5 to-primary/10 border border-primary/10">
                <div className="flex items-start gap-3">
                  <FileText className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <h3 className="text-base font-medium text-foreground mb-2">
                      Beschreibung
                    </h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {permission.beschreibung}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
