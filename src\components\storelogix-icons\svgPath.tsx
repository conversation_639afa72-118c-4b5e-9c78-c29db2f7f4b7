"use client"

import React from 'react';
import type { iconPathProps } from './sl-icons.types';



const SvgPath:React.FC<iconPathProps> = ({
    name,
    color
}) => {

    const svgColor = ['kiwi', 'berry', 'beach'].find(f => f == color) ? (`url(#${color})`) : (color);

    switch (name) {
        case 'add-photo':
            return (
                <>
                    <path fill={svgColor} d="M18.4726 3.75C18.4726 3.33579 18.1368 3 17.7226 3C17.3084 3 16.9726 3.33579 16.9726 3.75V4.97259H15.75C15.3358 4.97259 15 5.30838 15 5.72259C15 6.13681 15.3358 6.47259 15.75 6.47259H16.9726V7.69518C16.9726 8.1094 17.3084 8.44518 17.7226 8.44518C18.1368 8.44518 18.4726 8.1094 18.4726 7.69518V6.47259H19.6952C20.1094 6.47259 20.4452 6.13681 20.4452 5.72259C20.4452 5.30838 20.1094 4.97259 19.6952 4.97259H18.4726V3.75Z"/>
                    <path fill={svgColor} d="M8.87596 4.08397C9.01506 3.87533 9.24924 3.75 9.5 3.75H13C13.4142 3.75 13.75 4.08579 13.75 4.5C13.75 4.91421 13.4142 5.25 13 5.25H9.90139L8.45737 7.41603C8.31827 7.62467 8.0841 7.75 7.83334 7.75H4.5C4.25689 7.75 4.02373 7.84658 3.85182 8.01849C3.67991 8.19039 3.58334 8.42355 3.58334 8.66667V17.8333C3.58334 18.0764 3.67991 18.3096 3.85182 18.4815C4.02373 18.6534 4.25689 18.75 4.5 18.75H19.5C19.7431 18.75 19.9763 18.6534 20.1482 18.4815C20.3201 18.3096 20.4167 18.0764 20.4167 17.8333V8.66667C20.4167 8.25245 20.7525 7.91667 21.1667 7.91667C21.5809 7.91667 21.9167 8.25245 21.9167 8.66667V17.8333C21.9167 18.4743 21.6621 19.089 21.2088 19.5422C20.7556 19.9954 20.1409 20.25 19.5 20.25H4.5C3.85906 20.25 3.24437 19.9954 2.79116 19.5422C2.33795 19.089 2.08334 18.4743 2.08334 17.8333V8.66667C2.08334 8.02573 2.33795 7.41104 2.79116 6.95783C3.24437 6.50461 3.85906 6.25 4.5 6.25H7.43195L8.87596 4.08397Z"/>
                    <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M12 8.75C9.74484 8.75 7.91667 10.5782 7.91667 12.8333C7.91667 15.0885 9.74484 16.9167 12 16.9167C14.2552 16.9167 16.0833 15.0885 16.0833 12.8333C16.0833 10.5782 14.2552 8.75 12 8.75ZM9.41667 12.8333C9.41667 11.4066 10.5733 10.25 12 10.25C13.4267 10.25 14.5833 11.4066 14.5833 12.8333C14.5833 14.2601 13.4267 15.4167 12 15.4167C10.5733 15.4167 9.41667 14.2601 9.41667 12.8333Z"/>
                </>
            );
            break;

        case 'back':
            return(
                <>
                    <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M11.5303 7.53033C11.8232 7.23744 11.8232 6.76256 11.5303 6.46967C11.2374 6.17678 10.7626 6.17678 10.4697 6.46967L5.46967 11.4697C5.39776 11.5416 5.34351 11.6245 5.30691 11.7129C5.27024 11.8013 5.25 11.8983 5.25 12C5.25 12.1017 5.27024 12.1987 5.30691 12.2871C5.34351 12.3755 5.39776 12.4584 5.46967 12.5303L10.4697 17.5303C10.7626 17.8232 11.2374 17.8232 11.5303 17.5303C11.8232 17.2374 11.8232 16.7626 11.5303 16.4697L7.81066 12.75H18.5C18.9142 12.75 19.25 12.4142 19.25 12C19.25 11.5858 18.9142 11.25 18.5 11.25H7.81066L11.5303 7.53033Z"/>
                </>
            )
            break;
        case 'camera':
            return(
                <>
                    <path d="M14.5 4.5L16.1667 7H19.5C19.942 7 20.366 7.17559 20.6785 7.48816C20.9911 7.80072 21.1667 8.22464 21.1667 8.66667V17.8333C21.1667 18.2754 20.9911 18.6993 20.6785 19.0118C20.366 19.3244 19.942 19.5 19.5 19.5H4.5C4.05797 19.5 3.63405 19.3244 3.32149 19.0118C3.00893 18.6993 2.83333 18.2754 2.83333 17.8333V8.66667C2.83333 8.22464 3.00893 7.80072 3.32149 7.48816C3.63405 7.17559 4.05797 7 4.5 7H7.83333L9.5 4.5H11.5" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M12 16.1667C13.841 16.1667 15.3333 14.6743 15.3333 12.8333C15.3333 10.9924 13.841 9.5 12 9.5C10.1591 9.5 8.66667 10.9924 8.66667 12.8333C8.66667 14.6743 10.1591 16.1667 12 16.1667Z" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </>
            )
            break;
        case 'chat':
            return(
                <>
                    <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M6.75 9.373C6.75 5.84101 9.93769 2.883 14 2.883C18.0623 2.883 21.25 5.84101 21.25 9.373C21.25 11.4669 20.1386 13.3377 18.3791 14.5349C18.1736 14.6748 18.0507 14.9074 18.051 15.156C18.0516 15.591 18.0512 16.1121 18.0507 16.6806L18.0507 16.6845L18.0506 16.7949L15.9924 15.7812C15.846 15.7091 15.68 15.6867 15.5197 15.7174C15.0273 15.8119 14.5196 15.863 14 15.863C13.5254 15.863 13.0617 15.8217 12.6127 15.7432C12.0774 13.1777 9.80329 11.25 7.078 11.25C7.07187 11.25 7.06575 11.25 7.05962 11.25C6.85861 10.6559 6.75 10.0263 6.75 9.373ZM6.60421 12.7767C6.62518 12.7754 6.64617 12.7732 6.66715 12.7701C6.8023 12.7568 6.93936 12.75 7.078 12.75C9.21057 12.75 10.9671 14.3566 11.2047 16.4248C11.206 16.4434 11.208 16.4619 11.2107 16.4803C11.2248 16.6196 11.232 16.7609 11.232 16.904C11.232 19.1978 9.37179 21.058 7.078 21.058C7.05116 21.058 7.02464 21.0594 6.99852 21.0622C6.40341 21.0503 5.84104 20.9138 5.33333 20.6785C5.18616 20.6103 5.02085 20.5917 4.86219 20.6254L2.96909 21.0274L3.36519 19.1292C3.39821 18.971 3.37922 18.8063 3.31105 18.6597C3.06301 18.1264 2.924 17.5321 2.924 16.904C2.924 14.7703 4.53275 13.0119 6.60421 12.7767ZM5.55233 11.4582C5.35589 10.7946 5.25 10.0957 5.25 9.373C5.25 4.87099 9.25832 1.383 14 1.383C18.7417 1.383 22.75 4.87099 22.75 9.373C22.75 11.8872 21.4824 14.092 19.5513 15.5404C19.5513 15.8884 19.551 16.2762 19.5507 16.6879V16.6888L19.5507 16.6993C19.5504 17.1141 19.55 17.5531 19.55 18C19.55 18.2591 19.4162 18.4999 19.1962 18.6368C18.9762 18.7737 18.7011 18.7873 18.4686 18.6728L15.5503 17.2355C15.0472 17.3184 14.5295 17.363 14 17.363C13.5656 17.363 13.1381 17.3337 12.7199 17.2773C12.5297 20.193 10.1287 22.5064 7.17716 22.5571C7.14632 22.561 7.11489 22.563 7.083 22.563C6.32467 22.563 5.60079 22.4133 4.93896 22.1425L2.15582 22.7336C1.9079 22.7863 1.65016 22.7102 1.47062 22.5313C1.29108 22.3524 1.21405 22.0949 1.26582 21.8468L1.84795 19.057C1.57483 18.3924 1.424 17.665 1.424 16.904C1.424 14.3104 3.17048 12.124 5.55233 11.4582Z"/>
                </>
            )
            break;
        case 'close':
            return(
                <>
                <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M8.53033 7.46967C8.23744 7.17678 7.76256 7.17678 7.46967 7.46967C7.17678 7.76256 7.17678 8.23744 7.46967 8.53033L10.9393 12L7.46967 15.4697C7.17678 15.7626 7.17678 16.2374 7.46967 16.5303C7.76256 16.8232 8.23744 16.8232 8.53033 16.5303L12 13.0607L15.4697 16.5303C15.7626 16.8232 16.2374 16.8232 16.5303 16.5303C16.8232 16.2374 16.8232 15.7626 16.5303 15.4697L13.0607 12L16.5303 8.53033C16.8232 8.23744 16.8232 7.76256 16.5303 7.46967C16.2374 7.17678 15.7626 7.17678 15.4697 7.46967L12 10.9393L8.53033 7.46967Z"/>
                
                </>
            );
            break;
        case 'contact':
            return(
                <>
                    <path  stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M7 4H15H18C19.6569 4 21 5.34314 21 7V13.6129C21 15.2698 19.6569 16.6129 18 16.6129H14.5L10.5 21V16.6129H7C5.34315 16.6129 4 15.2698 4 13.6129V7C4 5.34315 5.34315 4 7 4Z"/>
                </>
            );
            break;
        case 'dropdown':
            return (
                <>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M8 14L12 10L16 14"/>
                </>
            );
            break;
        case 'edit':
            return(
                <>
                    <path  fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M16.1493 5.82333C16.2464 5.72623 16.4046 5.72623 16.5017 5.82333L18.1777 7.49933C18.2748 7.59642 18.2748 7.75451 18.1777 7.85162L18.1777 7.85167L16.8399 9.18921L14.8117 7.161L16.1493 5.82333ZM13.2535 6.59783C13.242 6.60803 13.2307 6.61864 13.2197 6.62967C13.2086 6.6407 13.198 6.65199 13.1878 6.66352L4.76409 15.0873L4.7633 15.088C4.4335 15.4165 4.25 15.8619 4.25 16.325V19C4.25 19.4142 4.58579 19.75 5 19.75H7.675C8.13818 19.75 8.58359 19.5665 8.91206 19.2366L8.91274 19.2359L17.3561 10.7942C17.3609 10.7896 17.3656 10.785 17.3703 10.7803C17.375 10.7757 17.3796 10.771 17.3841 10.7662L19.2383 8.91238L19.2383 8.91233C19.9212 8.22944 19.9212 7.12157 19.2383 6.43867L17.5623 4.76267C16.8794 4.07978 15.7716 4.07978 15.0887 4.76267L13.2535 6.59783ZM15.7791 10.2498L7.85072 18.1766L7.84926 18.1781C7.8038 18.2238 7.7415 18.25 7.675 18.25H5.75V16.325C5.75 16.2585 5.77621 16.1962 5.82191 16.1507L5.82333 16.1493L13.751 8.22166L15.7791 10.2498Z"/>
                </>
            );
            break;
        case 'enter':
            return(
                <>
                    <path  fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M7.53033 6.75537C7.23744 6.46248 6.76256 6.46248 6.46967 6.75537C6.17678 7.04826 6.17678 7.52314 6.46967 7.81603L10.9393 12.2857L6.46967 16.7554C6.17678 17.0483 6.17678 17.5231 6.46967 17.816C6.76256 18.1089 7.23744 18.1089 7.53033 17.816L12.5303 12.816C12.8232 12.5231 12.8232 12.0483 12.5303 11.7554L7.53033 6.75537ZM14.0303 7.46967C13.7374 7.17678 13.2626 7.17678 12.9697 7.46967C12.6768 7.76256 12.6768 8.23744 12.9697 8.53033L16.4393 12L12.9697 15.4697C12.6768 15.7626 12.6768 16.2374 12.9697 16.5303C13.2626 16.8232 13.7374 16.8232 14.0303 16.5303L18.0303 12.5303C18.3232 12.2374 18.3232 11.7626 18.0303 11.4697L14.0303 7.46967Z"/>
                </>
            );
            break;
        case 'eye':
            return (
                <>
                    <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d='M4.11971 10.4176C5.49798 7.60008 8.56983 5.75 12 5.75C15.4302 5.75 18.502 7.60008 19.8803 10.4176C20.1248 10.9175 20.25 11.4569 20.25 12C20.25 12.5431 20.1248 13.0825 19.8803 13.5824C18.502 16.3999 15.4302 18.25 12 18.25C8.56983 18.25 5.49798 16.3999 4.11971 13.5824C3.87516 13.0825 3.75 12.5431 3.75 12C3.75 11.4569 3.87516 10.9175 4.11971 10.4176L3.43974 10.0849L4.11971 10.4176ZM12 4.25C8.04817 4.25 4.42402 6.38192 2.77229 9.75843C2.42884 10.4605 2.25 11.2251 2.25 12C2.25 12.7749 2.42884 13.5395 2.77229 14.2416C4.42402 17.6181 8.04817 19.75 12 19.75C15.9518 19.75 19.576 17.6181 21.2277 14.2416C21.5712 13.5395 21.75 12.7749 21.75 12C21.75 11.2251 21.5712 10.4605 21.2277 9.75843C19.576 6.38192 15.9518 4.25 12 4.25ZM10.4076 10.4101C11.2878 9.5301 12.7126 9.53051 13.5914 10.4101L13.5917 10.4103C14.4694 11.288 14.4702 12.7142 13.5917 13.5947C12.7139 14.4724 11.2877 14.4732 10.4072 13.5945C9.52954 12.7167 9.52887 11.2905 10.4076 10.4101ZM9.34667 9.34967C10.8121 7.88426 13.1863 7.88358 14.6515 9.34881L14.6526 9.3499C16.1165 10.8141 16.1157 13.1894 14.6528 14.6549L14.6523 14.6553C13.1882 16.1195 10.8127 16.1188 9.34712 14.6558L9.34667 14.6553C7.88253 13.1912 7.88318 10.8157 9.34622 9.35012L9.34667 9.34967Z'/>
                </>
            );
            break;
        case 'list':
            return(
                <>
                    <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M3.5 5.25C3.08579 5.25 2.75 5.58579 2.75 6C2.75 6.41421 3.08579 6.75 3.5 6.75H4.5C4.91421 6.75 5.25 6.41421 5.25 6C5.25 5.58579 4.91421 5.25 4.5 5.25H3.5ZM8.5 5.25C8.08579 5.25 7.75 5.58579 7.75 6C7.75 6.41421 8.08579 6.75 8.5 6.75H20.5C20.9142 6.75 21.25 6.41421 21.25 6C21.25 5.58579 20.9142 5.25 20.5 5.25H8.5ZM2.75 12C2.75 11.5858 3.08579 11.25 3.5 11.25H4.5C4.91421 11.25 5.25 11.5858 5.25 12C5.25 12.4142 4.91421 12.75 4.5 12.75H3.5C3.08579 12.75 2.75 12.4142 2.75 12ZM3.5 17.25C3.08579 17.25 2.75 17.5858 2.75 18C2.75 18.4142 3.08579 18.75 3.5 18.75H4.5C4.91421 18.75 5.25 18.4142 5.25 18C5.25 17.5858 4.91421 17.25 4.5 17.25H3.5ZM7.75 12C7.75 11.5858 8.08579 11.25 8.5 11.25H20.5C20.9142 11.25 21.25 11.5858 21.25 12C21.25 12.4142 20.9142 12.75 20.5 12.75H8.5C8.08579 12.75 7.75 12.4142 7.75 12ZM8.5 17.25C8.08579 17.25 7.75 17.5858 7.75 18C7.75 18.4142 8.08579 18.75 8.5 18.75H20.5C20.9142 18.75 21.25 18.4142 21.25 18C21.25 17.5858 20.9142 17.25 20.5 17.25H8.5Z"/>
                </>
            );
            break;
        case 'lock':
            return(
                <>
                    <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M8.75 7C8.75 5.20521 10.2052 3.75 12 3.75C13.7948 3.75 15.25 5.20521 15.25 7V8.25H8.75V7ZM7.25 8.25V7C7.25 4.37679 9.37679 2.25 12 2.25C14.6232 2.25 16.75 4.37679 16.75 7V8.25H17C18.5192 8.25 19.75 9.48079 19.75 11V19C19.75 20.5192 18.5192 21.75 17 21.75H7C5.48079 21.75 4.25 20.5192 4.25 19V11C4.25 9.48079 5.48079 8.25 7 8.25H7.25ZM16 9.75H8H7C6.30921 9.75 5.75 10.3092 5.75 11V19C5.75 19.6908 6.30921 20.25 7 20.25H17C17.6908 20.25 18.25 19.6908 18.25 19V11C18.25 10.3092 17.6908 9.75 17 9.75H16ZM13.0606 14.8107C12.9656 14.9057 12.861 14.9853 12.75 15.0495V17.09C12.75 17.5042 12.4142 17.84 12 17.84C11.5858 17.84 11.25 17.5042 11.25 17.09V15.0495C11.1389 14.9853 11.0343 14.9057 10.9393 14.8107C10.3535 14.2249 10.3535 13.2752 10.9393 12.6894C11.5251 12.1036 12.4748 12.1036 13.0606 12.6894C13.6464 13.2752 13.6464 14.2249 13.0606 14.8107Z"/>
                </>
            );
            break;
        case 'logout':
            return(
                <>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M12 4H9C7.34315 4 6 5.34315 6 7V17C6 18.6569 7.34315 20 9 20H12"/>
                    <path  stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M11 12H19M19 12L15.3077 8M19 12L15.3077 16"/>
                </>
            );
            break;
        case 'minus':
            return(
                <>
                    <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M7.25 12C7.25 11.5858 7.58579 11.25 8 11.25H16C16.4142 11.25 16.75 11.5858 16.75 12C16.75 12.4142 16.4142 12.75 16 12.75H8C7.58579 12.75 7.25 12.4142 7.25 12Z"/>
                </>
            );
            break;
        case 'next':
            return(
                <>
                    <path  fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M12 3.75C7.44321 3.75 3.75 7.44321 3.75 12C3.75 16.5568 7.44321 20.25 12 20.25C16.5568 20.25 20.25 16.5568 20.25 12C20.25 7.44321 16.5568 3.75 12 3.75ZM2.25 12C2.25 6.61479 6.61479 2.25 12 2.25C17.3852 2.25 21.75 6.61479 21.75 12C21.75 17.3852 17.3852 21.75 12 21.75C6.61479 21.75 2.25 17.3852 2.25 12ZM7.25 12C7.25 11.5858 7.58579 11.25 8 11.25H14.1893L12.4697 9.53033C12.1768 9.23744 12.1768 8.76256 12.4697 8.46967C12.7626 8.17678 13.2374 8.17678 13.5303 8.46967L16.5303 11.4697C16.6022 11.5416 16.6565 11.6245 16.6931 11.7129C16.7298 11.8013 16.75 11.8983 16.75 12C16.75 12.1017 16.7298 12.1987 16.6931 12.2871C16.6565 12.3755 16.6022 12.4584 16.5303 12.5303L13.5303 15.5303C13.2374 15.8232 12.7626 15.8232 12.4697 15.5303C12.1768 15.2374 12.1768 14.7626 12.4697 14.4697L14.1893 12.75H8C7.58579 12.75 7.25 12.4142 7.25 12Z"/>
                </>
            );
            break;
        case 'pause':
            return(
                <>
                    <path  stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M10.3333 6H7V18H10.3333V6Z"/>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M17 6H13.6667V18H17V6Z"/>
                </>
            );
            break;
        case 'plus':
            return(
                <>
                    <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M12.75 8C12.75 7.58579 12.4142 7.25 12 7.25C11.5858 7.25 11.25 7.58579 11.25 8V11.25H8C7.58579 11.25 7.25 11.5858 7.25 12C7.25 12.4142 7.58579 12.75 8 12.75H11.25V16C11.25 16.4142 11.5858 16.75 12 16.75C12.4142 16.75 12.75 16.4142 12.75 16V12.75H16C16.4142 12.75 16.75 12.4142 16.75 12C16.75 11.5858 16.4142 11.25 16 11.25H12.75V8Z"/>
                </>
            );
            break;
        case 'scan':
            return(
                <>
                    <path  fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M2.25004 5C2.25004 3.48079 3.48082 2.25 5.00004 2.25H8.00004C8.41425 2.25 8.75004 2.58579 8.75004 3C8.75004 3.41421 8.41425 3.75 8.00004 3.75H5.00004C4.30925 3.75 3.75004 4.30921 3.75004 5V8C3.75004 8.41421 3.41425 8.75 3.00004 8.75C2.58582 8.75 2.25004 8.41421 2.25004 8V5ZM15.2499 3C15.2499 2.58579 15.5857 2.25 15.9999 2.25H18.9999C20.5191 2.25 21.7499 3.48079 21.7499 5V8C21.7499 8.41421 21.4141 8.75 20.9999 8.75C20.5857 8.75 20.2499 8.41421 20.2499 8V5C20.2499 4.30921 19.6907 3.75 18.9999 3.75H15.9999C15.5857 3.75 15.2499 3.41421 15.2499 3ZM3.75004 16C3.75004 15.5858 3.41425 15.25 3.00004 15.25C2.58582 15.25 2.25004 15.5858 2.25004 16V19C2.25004 20.5192 3.48083 21.75 5.00004 21.75H8.00004C8.41425 21.75 8.75004 21.4142 8.75004 21C8.75004 20.5858 8.41425 20.25 8.00004 20.25H5.00004C4.30925 20.25 3.75004 19.6908 3.75004 19V16ZM20.9999 15.25C21.4141 15.25 21.7499 15.5858 21.7499 16V19C21.7499 20.5192 20.5191 21.75 18.9999 21.75H15.9999C15.5857 21.75 15.2499 21.4142 15.2499 21C15.2499 20.5858 15.5857 20.25 15.9999 20.25H18.9999C19.6907 20.25 20.2499 19.6908 20.2499 19V16C20.2499 15.5858 20.5857 15.25 20.9999 15.25ZM3.00004 11.25C2.58582 11.25 2.25004 11.5858 2.25004 12C2.25004 12.4142 2.58582 12.75 3.00004 12.75H21C21.4142 12.75 21.75 12.4142 21.75 12C21.75 11.5858 21.4142 11.25 21 11.25H3.00004Z"/>
                </>
            );
            break;
        case 'token':
            return(
                <>
                    <path  fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M7 3.75C5.20521 3.75 3.75 5.20521 3.75 7V17C3.75 18.7948 5.20521 20.25 7 20.25H17C18.7948 20.25 20.25 18.7948 20.25 17V7C20.25 5.20521 18.7948 3.75 17 3.75H7ZM2.25 7C2.25 4.37679 4.37679 2.25 7 2.25H17C19.6232 2.25 21.75 4.37679 21.75 7V17C21.75 19.6232 19.6232 21.75 17 21.75H7C4.37679 21.75 2.25 19.6232 2.25 17V7ZM10.9908 9.8729C10.8361 10.2169 10.75 10.5984 10.75 11C10.75 12.5188 11.9812 13.75 13.5 13.75C13.516 13.75 13.532 13.7499 13.548 13.7496C13.5972 13.7424 13.6474 13.7402 13.6977 13.743C15.1249 13.6418 16.25 12.4525 16.25 11C16.25 9.48107 15.0196 8.25 13.5 8.25C12.4125 8.25 11.4725 8.88123 11.0263 9.79727C11.0158 9.82321 11.004 9.84845 10.9908 9.8729ZM9.27348 10.5505C8.36958 10.9988 7.75 11.9264 7.75 13C7.75 14.5188 8.98121 15.75 10.5 15.75C11.1856 15.75 11.8116 15.4965 12.2927 15.0761C10.5336 14.5558 9.25 12.9278 9.25 11C9.25 10.8482 9.25796 10.6982 9.27348 10.5505ZM14.1212 15.205C13.3785 16.4248 12.0423 17.25 10.5 17.25C8.15279 17.25 6.25 15.3472 6.25 13C6.25 10.87 7.81894 9.12297 9.85529 8.81275C10.5986 7.57683 11.9527 6.75 13.5 6.75C15.8484 6.75 17.75 8.65293 17.75 11C17.75 13.1361 16.1748 14.9044 14.1212 15.205Z"/>
                </>
            );
            break;
        case 'trash':
            return(
                <>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M4 6H5.77778H20"/>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M19 6.6V20.2C19 20.6774 18.7893 21.1352 18.4142 21.4728C18.0391 21.8104 17.5304 22 17 22H7C6.46957 22 5.96086 21.8104 5.58579 21.4728C5.21071 21.1352 5 20.6774 5 20.2V6.6M8 5.6V3.8C8 3.32261 8.21071 2.86477 8.58579 2.52721C8.96086 2.18964 9.46957 2 10 2H14C14.5304 2 15.0391 2.18964 15.4142 2.52721C15.7893 2.86477 16 3.32261 16 3.8V5.6"/>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M10 11V17"/>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M14 11V17" />

                </>
            );
            break;
        case 'user':
            return(
                <>
                    <path  fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M10.0555 5.05546C11.1294 3.98151 12.8706 3.98151 13.9446 5.05546C15.0185 6.12941 15.0185 7.8706 13.9446 8.94455C12.8706 10.0185 11.1294 10.0185 10.0555 8.94455C8.98154 7.8706 8.98154 6.12941 10.0555 5.05546ZM8.99482 3.9948C10.6546 2.33507 13.3455 2.33507 15.0052 3.9948C16.665 5.65453 16.665 8.34548 15.0052 10.0052C13.3455 11.6649 10.6546 11.6649 8.99482 10.0052C7.33509 8.34548 7.33509 5.65453 8.99482 3.9948ZM12 12.758C9.86712 12.758 7.7321 13.2737 6.10466 14.2211C4.48397 15.1646 3.25 16.6183 3.25 18.5V19.5C3.25 20.4662 4.03379 21.25 5 21.25H19C19.9662 21.25 20.75 20.4662 20.75 19.5V18.5C20.75 16.6183 19.516 15.1646 17.8953 14.2211C16.2679 13.2737 14.1329 12.758 12 12.758ZM4.75 18.5C4.75 17.3557 5.49203 16.3134 6.85934 15.5174C8.2199 14.7253 10.0849 14.258 12 14.258C13.9151 14.258 15.7801 14.7253 17.1407 15.5174C18.508 16.3134 19.25 17.3557 19.25 18.5V19.5C19.25 19.6378 19.1378 19.75 19 19.75H5C4.86221 19.75 4.75 19.6378 4.75 19.5V18.5Z"/>
                </>
            );
            break;
        case 'payGrow':
            return(
                <>
                    <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M10.5272 4.04008C11.4711 3.65363 12.5289 3.65363 13.4728 4.04008L13.473 4.04013L18.4727 6.08604L18.473 6.08613C18.9435 6.27896 19.25 6.73594 19.25 7.24299V11.182C19.25 15.3093 16.1701 19.2101 12 20.2297C7.82992 19.2101 4.75 15.3093 4.75 11.182V7.24299C4.75 6.73527 5.05704 6.278 5.52647 6.08636L5.52704 6.08613L10.527 4.04013L10.5272 4.04008ZM9.95896 2.65186C11.267 2.11637 12.733 2.11637 14.041 2.65186L14.0412 2.65191L19.041 4.69786L19.0413 4.69795C20.0746 5.12117 20.75 6.12613 20.75 7.24299V11.182C20.75 16.0826 17.0852 20.6257 12.1645 21.7317C12.0562 21.7561 11.9438 21.7561 11.8355 21.7317C6.91484 20.6257 3.25 16.0826 3.25 11.182V7.24299C3.25 6.1268 3.92486 5.12013 4.95931 4.69772L4.95953 4.69763L9.95883 2.65191L9.95896 2.65186ZM12.75 7.49999C12.75 7.08578 12.4142 6.74999 12 6.74999C11.5858 6.74999 11.25 7.08578 11.25 7.49999V7.75442C10.1329 7.83335 9.25 8.76429 9.25 9.903C9.25 10.8912 9.9232 11.7517 10.8793 11.9914L10.8795 11.9915L10.8798 11.9915L12.7529 12.4621L12.7534 12.4622C13.0461 12.5365 13.25 12.7993 13.25 13.098C13.25 13.4591 12.9574 13.752 12.596 13.752H12.0552C12.037 13.7507 12.0186 13.75 12 13.75C11.9814 13.75 11.963 13.7507 11.9448 13.752H11.5C11.2605 13.752 11.0456 13.6258 10.904 13.394C10.6881 13.0405 10.2265 12.929 9.87303 13.145C9.51955 13.3609 9.40804 13.8225 9.62396 14.176C9.96184 14.7291 10.5355 15.1562 11.25 15.2378V15.5C11.25 15.9142 11.5858 16.25 12 16.25C12.4142 16.25 12.75 15.9142 12.75 15.5V15.2466C13.8685 15.1676 14.75 14.2352 14.75 13.098C14.75 12.1089 14.0761 11.2498 13.1211 11.0079L13.1197 11.0076L11.2447 10.5366L11.2442 10.5365C10.9545 10.4639 10.75 10.2026 10.75 9.903C10.75 9.54156 11.0429 9.24899 11.404 9.24899H11.9609C11.9739 9.24966 11.9869 9.24999 12 9.24999C12.0131 9.24999 12.0261 9.24966 12.0391 9.24899H12.5C12.7383 9.24899 12.9526 9.3749 13.0927 9.60453C13.3084 9.95816 13.7699 10.07 14.1235 9.85429C14.4772 9.63861 14.589 9.17709 14.3733 8.82346C14.036 8.27043 13.4622 7.84481 12.75 7.76318V7.49999Z"/>
                </>
            );
            break;
        case 'process':
            return(
                <>
                    <path  fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M4 3.75C3.86221 3.75 3.75 3.86221 3.75 4V6C3.75 6.13779 3.86221 6.25 4 6.25H6C6.13779 6.25 6.25 6.13779 6.25 6V4C6.25 3.86221 6.13779 3.75 6 3.75H4ZM2.25 4C2.25 3.03379 3.03379 2.25 4 2.25H6C6.96621 2.25 7.75 3.03379 7.75 4V6C7.75 6.96621 6.96621 7.75 6 7.75H4C3.03379 7.75 2.25 6.96621 2.25 6V4ZM18 17.75C17.8622 17.75 17.75 17.8622 17.75 18V20C17.75 20.1378 17.8622 20.25 18 20.25H20C20.1378 20.25 20.25 20.1378 20.25 20V18C20.25 17.8622 20.1378 17.75 20 17.75H18ZM16.25 18C16.25 17.0338 17.0338 16.25 18 16.25H20C20.9662 16.25 21.75 17.0338 21.75 18V20C21.75 20.9662 20.9662 21.75 20 21.75H18C17.0338 21.75 16.25 20.9662 16.25 20V18ZM10.75 11C10.75 10.8622 10.8622 10.75 11 10.75H13C13.1378 10.75 13.25 10.8622 13.25 11V13C13.25 13.1378 13.1378 13.25 13 13.25H11C10.8622 13.25 10.75 13.1378 10.75 13V11ZM11 9.25C10.0338 9.25 9.25 10.0338 9.25 11V13C9.25 13.9662 10.0338 14.75 11 14.75H13C13.9662 14.75 14.75 13.9662 14.75 13V11C14.75 10.0338 13.9662 9.25 13 9.25H11ZM10.25 5C10.25 4.58579 10.5858 4.25 11 4.25H19C20.5192 4.25 21.75 5.48079 21.75 7V10C21.75 11.5192 20.5192 12.75 19 12.75H17C16.5858 12.75 16.25 12.4142 16.25 12C16.25 11.5858 16.5858 11.25 17 11.25H19C19.6908 11.25 20.25 10.6908 20.25 10V7C20.25 6.30921 19.6908 5.75 19 5.75H11C10.5858 5.75 10.25 5.41421 10.25 5ZM3.75 14C3.75 13.3092 4.30921 12.75 5 12.75H7C7.41421 12.75 7.75 12.4142 7.75 12C7.75 11.5858 7.41421 11.25 7 11.25H5C3.48079 11.25 2.25 12.4808 2.25 14V17C2.25 18.5192 3.48079 19.75 5 19.75H13C13.4142 19.75 13.75 19.4142 13.75 19C13.75 18.5858 13.4142 18.25 13 18.25H5C4.30921 18.25 3.75 17.6908 3.75 17V14Z"/>
                </>
            );
            break;
        case 'quote':
            return(
                <>
                    <path  fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M6.16643 6.16637C9.38826 2.94454 14.6119 2.94454 17.8337 6.16637L17.834 6.16671C21.0555 9.38857 21.0554 14.6119 17.8337 17.8336C14.6118 21.0554 9.38824 21.0554 6.16643 17.8336C2.9446 14.6118 2.9446 9.38818 6.16643 6.16637L6.16643 6.16637ZM18.8943 18.8943C22.7019 15.0867 22.7019 8.91332 18.8943 5.10571C15.0867 1.2981 8.91338 1.2981 5.10577 5.10571C1.29816 8.9133 1.29816 15.0867 5.10577 18.8943C8.91336 22.7019 15.0867 22.7019 18.8943 18.8943ZM9.63249 9.64513C9.98879 9.43389 10.1064 8.9738 9.89513 8.6175C9.68389 8.2612 9.2238 8.14362 8.86751 8.35486C8.31863 8.68028 7.76964 9.0188 7.42446 9.6324C7.08779 10.2309 7 10.9861 7 12V14.5C7 15.1902 7.55979 15.75 8.25 15.75H10.25C10.9402 15.75 11.5 15.1902 11.5 14.5V12.5C11.5 11.8098 10.9402 11.25 10.25 11.25H8.52566C8.56349 10.7806 8.63979 10.5314 8.73179 10.3678C8.85536 10.1482 9.05637 9.98671 9.63249 9.64513ZM8.5 14.25V12.75H10V14.25H8.5ZM15.1325 9.64513C15.4888 9.43389 15.6064 8.9738 15.3951 8.6175C15.1839 8.2612 14.7238 8.14362 14.3675 8.35486C13.8186 8.68028 13.2696 9.0188 12.9245 9.6324C12.5878 10.2309 12.5 10.9861 12.5 12V14.5C12.5 15.1902 13.0598 15.75 13.75 15.75H15.75C16.4402 15.75 17 15.1902 17 14.5V12.5C17 11.8098 16.4402 11.25 15.75 11.25H14.0257C14.0635 10.7806 14.1398 10.5314 14.2318 10.3678C14.3554 10.1482 14.5564 9.98671 15.1325 9.64513ZM14 14.25V12.75H15.5V14.25H14Z"/>
                </>
            );
            break;
        case 'rocket':
            return(
                <>
                    <path  fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M21.4213 5.23968L21.4213 5.23959C21.7512 3.65287 20.3471 2.2488 18.7604 2.57868L18.7603 2.5787L14.8724 3.38668L14.8724 3.38669C13.5845 3.65437 12.4032 4.29213 11.4738 5.22258L11.4736 5.22273L11.4734 5.22298L8.86107 7.83444L5.75228 7.27854L5.75805 8.02852L5.75145 7.27855L5.75176 7.27855C5.27633 7.28234 4.82546 7.47626 4.49473 7.81096L1.61926 10.7209L1.61831 10.7219C0.928513 11.4225 1.25123 12.6316 2.22791 12.8651L5.76859 13.7115C5.69916 14.6792 5.96483 15.6368 6.51294 16.4264L5.33967 17.5997C5.04678 17.8926 5.04678 18.3674 5.33967 18.6603C5.63257 18.9532 6.10744 18.9532 6.40033 18.6603L7.57345 17.4872C8.36282 18.0352 9.32079 18.3006 10.2883 18.2306L11.1349 21.7721C11.3684 22.7488 12.5775 23.0715 13.2781 22.3817L13.2791 22.3807L16.189 19.5053C16.5237 19.1746 16.7176 18.7239 16.7214 18.2485L16.7214 18.2484L16.7215 18.2477L16.75 15.0066C16.7511 14.8752 16.7185 14.7515 16.6601 14.6436L18.777 12.5266C19.7077 11.5971 20.3456 10.4156 20.6133 9.12762L19.879 8.97499L20.6133 9.12759L21.4213 5.23968ZM15.2408 16.0444C14.2415 16.9399 13.0512 17.5923 11.7643 17.9533L12.5021 21.0397L15.1347 18.4383C15.1909 18.3828 15.2209 18.3098 15.2215 18.2362L15.2215 18.2353L15.2408 16.0444ZM8.66114 16.3995C9.17713 16.6856 9.77908 16.8002 10.377 16.7131L10.3785 16.7129L10.4483 16.7024L10.4515 16.7019L10.4515 16.7019C11.9946 16.4763 13.4247 15.7577 14.5277 14.6547L17.7167 11.4657L17.717 11.4653C17.972 11.2107 18.1987 10.9317 18.3945 10.6332C17.0078 8.69278 15.3072 6.99219 13.3668 5.60545C13.0683 5.8013 12.7893 6.02803 12.5347 6.283L12.5343 6.2834L9.34433 9.47232L9.34425 9.4724C8.24151 10.5752 7.52365 12.0053 7.29705 13.5489L7.29663 13.5517L7.28621 13.6209L7.28594 13.6227C7.19943 14.2207 7.31422 14.8224 7.60062 15.3387L12.4097 10.5297C12.7026 10.2368 13.1774 10.2368 13.4703 10.5297C13.7632 10.8226 13.7632 11.2974 13.4703 11.5903L8.66114 16.3995ZM14.944 4.90945C16.4958 6.11005 17.8899 7.50423 19.0905 9.05601C19.1103 8.97868 19.1284 8.90079 19.1447 8.82239L19.1447 8.82236L19.9527 4.93439L19.9527 4.9343C20.0628 4.40509 19.5949 3.93724 19.0657 4.04728L19.0656 4.0473L15.1776 4.8553L15.1776 4.8553C15.0992 4.8716 15.0213 4.88966 14.944 4.90945ZM6.04612 12.2356C6.4073 10.949 7.05907 9.75863 7.95464 8.75922L5.76465 8.77849L5.76382 8.7785C5.69024 8.77906 5.61718 8.80913 5.5617 8.86527L2.96027 11.4979L6.04612 12.2356Z"/>             
                </>
            );
            break;
        case 'time':
            return(
                <>
                    <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M9.25 2C9.25 1.58579 9.58579 1.25 10 1.25H14C14.4142 1.25 14.75 1.58579 14.75 2C14.75 2.41421 14.4142 2.75 14 2.75H10C9.58579 2.75 9.25 2.41421 9.25 2ZM17.1257 7.87236C14.2945 5.04222 9.70411 5.04255 6.87333 7.87333C4.04222 10.7044 4.04222 15.2956 6.87333 18.1267C9.70444 20.9578 14.2956 20.9578 17.1267 18.1267C19.9578 15.2956 19.9578 10.7044 17.1267 7.87333C17.1263 7.87301 17.126 7.87268 17.1257 7.87236ZM17.6346 6.30475C14.1969 3.40512 9.05092 3.57442 5.81267 6.81267C2.39578 10.2296 2.39578 15.7704 5.81267 19.1873C9.22956 22.6042 14.7704 22.6042 18.1873 19.1873C21.4256 15.9491 21.5949 10.8031 18.6952 7.36541L20 6.06066L20.4697 6.53033C20.7626 6.82322 21.2374 6.82322 21.5303 6.53033C21.8232 6.23744 21.8232 5.76256 21.5303 5.46967L20.5303 4.46967L19.5303 3.46967C19.2374 3.17678 18.7626 3.17678 18.4697 3.46967C18.1768 3.76256 18.1768 4.23744 18.4697 4.53033L18.9393 5L17.6346 6.30475ZM12.75 11.9989C13.0535 12.2264 13.25 12.589 13.25 12.998L13.25 13.0017C13.2475 13.6893 12.6907 14.25 11.999 14.25C11.3088 14.25 10.75 13.6912 10.75 13.001C10.75 12.5916 10.9466 12.2289 11.25 12.0008V9C11.25 8.58579 11.5858 8.25 12 8.25C12.4142 8.25 12.75 8.58579 12.75 9V11.9989Z"/>
                </>
            );
            break;
        case 'check':
            return(
                <>
                    <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M3.75 12C3.75 7.44321 7.44321 3.75 12 3.75C13.3298 3.75 14.5829 4.07278 15.699 4.63405C16.0691 4.82014 16.52 4.67101 16.706 4.30095C16.8921 3.9309 16.743 3.48005 16.373 3.29395C15.0591 2.63322 13.5762 2.25 12 2.25C6.61479 2.25 2.25 6.61479 2.25 12C2.25 17.3852 6.61479 21.75 12 21.75C17.3852 21.75 21.75 17.3852 21.75 12C21.75 11.5858 21.4142 11.25 21 11.25C20.5858 11.25 20.25 11.5858 20.25 12C20.25 16.5568 16.5568 20.25 12 20.25C7.44321 20.25 3.75 16.5568 3.75 12ZM20.0913 7.97433C20.3842 7.68144 20.3842 7.20656 20.0913 6.91367C19.7984 6.62078 19.3236 6.62078 19.0307 6.91367L12.0051 13.9393L8.7584 10.6917C8.46555 10.3988 7.99067 10.3987 7.69774 10.6916C7.40481 10.9845 7.40475 11.4593 7.6976 11.7523L11.4746 15.5303C11.6152 15.6709 11.806 15.75 12.005 15.75C12.2039 15.75 12.3947 15.671 12.5353 15.5303L20.0913 7.97433Z"/>
                </>
            );
            break;
        case 'checkmark':
            return(
                <>
                    <path  stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M21 6L9 18.5L3 12.75"/>
                </>
            );
            break;
        case 'loading':
            return(
                <>
                    <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M12 2.25C6.61522 2.25 2.25 6.61522 2.25 12C2.25 17.3848 6.61522 21.75 12 21.75C17.3848 21.75 21.75 17.3848 21.75 12C21.75 6.61652 17.3869 2.2521 12.0039 2.25H12ZM12 3.75C10.6761 3.75 9.42504 4.06184 8.31626 4.61607C8.92354 4.66291 9.5315 4.82101 10.1109 5.09931C10.119 5.1029 10.127 5.10662 10.1349 5.11049C12.0457 6.03947 13.171 8.06552 12.9479 10.1797L12.9478 10.1799L12.8836 10.7871L13.4403 10.5397C14.8048 9.93298 15.6434 8.5363 15.5369 7.04646L15.5359 7.02983C15.3964 5.18066 13.8557 3.75 12 3.75ZM3.76514 11.496C3.84432 10.1822 4.23099 8.95121 4.85416 7.87422L4.85552 7.875C5.78577 6.26378 7.80311 5.64555 9.4762 6.45857C9.48309 6.46192 9.49004 6.46516 9.49702 6.4683C10.8292 7.1252 11.6122 8.54297 11.4562 10.0221L11.4561 10.0223L11.3919 10.6295L10.8973 10.2696L10.8971 10.2694C9.17878 9.01975 6.8614 8.9798 5.10077 10.1708C5.09242 10.1764 5.08418 10.1823 5.07606 10.1882C4.54763 10.5498 4.1084 10.9956 3.76514 11.496ZM4.85416 16.1258C5.49437 17.2322 6.38418 18.1762 7.44661 18.8807C7.18096 18.3255 7.01337 17.7129 6.96692 17.0646L6.9658 17.0456C6.82136 14.9314 8.01363 12.9518 9.95028 12.0907L9.95042 12.0906L10.5092 11.8423L10.0149 11.4826L10.0147 11.4824C8.8134 10.6089 7.19559 10.5765 5.9607 11.4001C5.95363 11.4053 5.94648 11.4103 5.93924 11.4152C4.39825 12.4577 3.92523 14.5137 4.85552 16.125L4.85416 16.1258ZM12.6101 13.371L12.5459 13.9778L12.5458 13.9779C12.3898 15.4563 13.172 16.8749 14.5048 17.5316C14.5119 17.5348 14.5189 17.5381 14.5258 17.5414C16.1836 18.347 18.1794 17.7474 19.1207 16.1689C19.7588 15.0814 20.1548 13.8347 20.2349 12.5029C19.8917 13.0033 19.4524 13.4491 18.9239 13.8108C18.9186 13.8147 18.9131 13.8186 18.9077 13.8224C18.9049 13.8244 18.902 13.8263 18.8992 13.8282C17.1386 15.0192 14.8212 14.9793 13.1029 13.7296L13.1027 13.7294L12.6101 13.371ZM19.1454 7.87347C18.5052 6.76723 17.6154 5.82346 16.5531 5.11911C16.8189 5.67464 16.9866 6.2876 17.0331 6.93642L17.0342 6.95544C17.1786 9.06963 15.9864 11.0492 14.0497 11.9103L14.0496 11.9104L13.4925 12.158L13.9851 12.5164C15.1865 13.3901 16.8044 13.4225 18.0393 12.5989C18.0444 12.5952 18.0495 12.5916 18.0546 12.588C18.0567 12.5866 18.0587 12.5852 18.0608 12.5838C19.6017 11.5413 20.0748 9.48528 19.1445 7.874L19.1454 7.87347ZM10.5597 13.4613L11.1184 13.213L11.0542 13.8201L11.0541 13.8202C10.831 15.9332 11.955 17.9605 13.8672 18.8896C13.8751 18.8934 13.8831 18.8971 13.8911 18.9007C14.47 19.1787 15.0773 19.3368 15.684 19.3838C14.5752 19.9381 13.324 20.25 12 20.25C10.1442 20.25 8.60362 18.8192 8.4641 16.9712L8.46309 16.9545C8.35662 15.4647 9.19523 14.068 10.5597 13.4613Z"/>         
                </>
            );
            break;
        case 'success':
            return(
                <>
                    <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M3.75 12C3.75 7.44321 7.44321 3.75 12 3.75C16.5568 3.75 20.25 7.44321 20.25 12C20.25 16.5568 16.5568 20.25 12 20.25C7.44321 20.25 3.75 16.5568 3.75 12ZM12 2.25C6.61479 2.25 2.25 6.61479 2.25 12C2.25 17.3852 6.61479 21.75 12 21.75C17.3852 21.75 21.75 17.3852 21.75 12C21.75 6.61479 17.3852 2.25 12 2.25ZM16.5303 10.5303C16.8232 10.2374 16.8232 9.76256 16.5303 9.46967C16.2374 9.17678 15.7626 9.17678 15.4697 9.46967L11 13.9393L8.53033 11.4697C8.23744 11.1768 7.76256 11.1768 7.46967 11.4697C7.17678 11.7626 7.17678 12.2374 7.46967 12.5303L10.4697 15.5303C10.7626 15.8232 11.2374 15.8232 11.5303 15.5303L16.5303 10.5303Z"/>
                </>
            );
            break;
        case 'burger':
            return(
                <>
                    <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M2 6C1.58579 6 1.25 6.33579 1.25 6.75C1.25 7.16421 1.58579 7.5 2 7.5H22C22.4142 7.5 22.75 7.16421 22.75 6.75C22.75 6.33579 22.4142 6 22 6H2ZM1.25 11.75C1.25 11.3358 1.58579 11 2 11H22C22.4142 11 22.75 11.3358 22.75 11.75C22.75 12.1642 22.4142 12.5 22 12.5H2C1.58579 12.5 1.25 12.1642 1.25 11.75ZM1.25 16.75C1.25 16.3358 1.58579 16 2 16H22C22.4142 16 22.75 16.3358 22.75 16.75C22.75 17.1642 22.4142 17.5 22 17.5H2C1.58579 17.5 1.25 17.1642 1.25 16.75Z"/>
                </>
            );
            break;
        case 'help':
            return(
                <>
                    <path stroke={svgColor} strokeWidth="1.6875" strokeLinecap="round" strokeLinejoin="round" d="M7 8.21942C7.36294 7.146 8.07931 6.24086 9.02223 5.6643C9.96515 5.08775 11.0738 4.87699 12.1517 5.06936C13.2297 5.26174 14.2074 5.84482 14.9118 6.71534C15.6161 7.58586 16.0016 8.68764 16 9.82554C16 13.0378 11.3688 14.6439 11.3688 14.6439V16.25"/>
                    <path stroke={svgColor} strokeWidth="1.6875" strokeLinecap="round" strokeLinejoin="round" d="M11.5113 19.625H11.5"/>
                </>
            );
            break;
        case 'placeholder':
            return(
                <>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" fillRule="evenodd" clipRule="evenodd" d="M19.764 16.634L10.93 20.315C9.892 20.748 8.719 20.723 7.7 20.247L4.713 18.853C3.752 18.404 3.308 17.283 3.702 16.298L8.514 4.268C8.941 3.201 10.176 2.713 11.217 3.198L14.491 4.726C15.214 5.063 15.819 5.609 16.229 6.293L20.709 13.759C21.337 14.805 20.89 16.165 19.764 16.634V16.634Z"/>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M15.53 5.43L9.45 20.61"/>
                </>
            );
            break;
        case 'settings':
            return(
                <>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M12 14C13.1046 14 14 13.1046 14 12C14 10.8954 13.1046 10 12 10C10.8954 10 10 10.8954 10 12C10 13.1046 10.8954 14 12 14Z"/>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M18.0545 14.4545C17.9456 14.7013 17.9131 14.9751 17.9613 15.2405C18.0094 15.5059 18.1359 15.7508 18.3245 15.9436L18.3736 15.9927C18.5258 16.1447 18.6465 16.3252 18.7288 16.5238C18.8112 16.7225 18.8536 16.9354 18.8536 17.1505C18.8536 17.3655 18.8112 17.5784 18.7288 17.7771C18.6465 17.9757 18.5258 18.1562 18.3736 18.3082C18.2217 18.4603 18.0412 18.581 17.8425 18.6634C17.6439 18.7457 17.431 18.7881 17.2159 18.7881C17.0009 18.7881 16.7879 18.7457 16.5893 18.6634C16.3906 18.581 16.2102 18.4603 16.0582 18.3082L16.0091 18.2591C15.8163 18.0705 15.5714 17.9439 15.3059 17.8958C15.0405 17.8477 14.7668 17.8802 14.52 17.9891C14.278 18.0928 14.0716 18.265 13.9263 18.4845C13.7809 18.704 13.7029 18.9613 13.7018 19.2245V19.3636C13.7018 19.7976 13.5294 20.2138 13.2225 20.5207C12.9157 20.8276 12.4994 21 12.0655 21C11.6315 21 11.2152 20.8276 10.9084 20.5207C10.6015 20.2138 10.4291 19.7976 10.4291 19.3636V19.29C10.4228 19.0192 10.3351 18.7565 10.1775 18.5362C10.0199 18.3159 9.79969 18.1481 9.54545 18.0545C9.29868 17.9456 9.02493 17.9131 8.75952 17.9613C8.4941 18.0094 8.24919 18.1359 8.05636 18.3245L8.00727 18.3736C7.8553 18.5258 7.67483 18.6465 7.47617 18.7288C7.27752 18.8112 7.06459 18.8536 6.84955 18.8536C6.6345 18.8536 6.42157 18.8112 6.22292 18.7288C6.02426 18.6465 5.84379 18.5258 5.69182 18.3736C5.53967 18.2217 5.41898 18.0412 5.33663 17.8425C5.25428 17.6439 5.21189 17.431 5.21189 17.2159C5.21189 17.0009 5.25428 16.7879 5.33663 16.5893C5.41898 16.3906 5.53967 16.2102 5.69182 16.0582L5.74091 16.0091C5.92953 15.8163 6.05606 15.5714 6.10419 15.3059C6.15231 15.0405 6.11982 14.7668 6.01091 14.52C5.90719 14.278 5.73498 14.0716 5.51547 13.9263C5.29596 13.7809 5.03873 13.7029 4.77545 13.7018H4.63636C4.20237 13.7018 3.78616 13.5294 3.47928 13.2225C3.1724 12.9157 3 12.4994 3 12.0655C3 11.6315 3.1724 11.2152 3.47928 10.9084C3.78616 10.6015 4.20237 10.4291 4.63636 10.4291H4.71C4.98081 10.4228 5.24346 10.3351 5.46379 10.1775C5.68412 10.0199 5.85195 9.79969 5.94545 9.54545C6.05437 9.29868 6.08686 9.02493 6.03873 8.75952C5.99061 8.4941 5.86408 8.24919 5.67545 8.05636L5.62636 8.00727C5.47422 7.8553 5.35352 7.67483 5.27118 7.47617C5.18883 7.27752 5.14644 7.06459 5.14644 6.84955C5.14644 6.6345 5.18883 6.42157 5.27118 6.22292C5.35352 6.02426 5.47422 5.84379 5.62636 5.69182C5.77834 5.53967 5.95881 5.41898 6.15746 5.33663C6.35611 5.25428 6.56905 5.21189 6.78409 5.21189C6.99913 5.21189 7.21207 5.25428 7.41072 5.33663C7.60937 5.41898 7.78984 5.53967 7.94182 5.69182L7.99091 5.74091C8.18374 5.92953 8.42865 6.05606 8.69406 6.10419C8.95948 6.15231 9.23322 6.11982 9.48 6.01091H9.54545C9.78745 5.90719 9.99383 5.73498 10.1392 5.51547C10.2846 5.29596 10.3626 5.03873 10.3636 4.77545V4.63636C10.3636 4.20237 10.536 3.78616 10.8429 3.47928C11.1498 3.1724 11.566 3 12 3C12.434 3 12.8502 3.1724 13.1571 3.47928C13.464 3.78616 13.6364 4.20237 13.6364 4.63636V4.71C13.6374 4.97328 13.7154 5.23051 13.8608 5.45002C14.0062 5.66953 14.2126 5.84174 14.4545 5.94545C14.7013 6.05437 14.9751 6.08686 15.2405 6.03873C15.5059 5.99061 15.7508 5.86408 15.9436 5.67545L15.9927 5.62636C16.1447 5.47422 16.3252 5.35352 16.5238 5.27118C16.7225 5.18883 16.9354 5.14644 17.1505 5.14644C17.3655 5.14644 17.5784 5.18883 17.7771 5.27118C17.9757 5.35352 18.1562 5.47422 18.3082 5.62636C18.4603 5.77834 18.581 5.95881 18.6634 6.15746C18.7457 6.35611 18.7881 6.56905 18.7881 6.78409C18.7881 6.99913 18.7457 7.21207 18.6634 7.41072C18.581 7.60937 18.4603 7.78984 18.3082 7.94182L18.2591 7.99091C18.0705 8.18374 17.9439 8.42865 17.8958 8.69406C17.8477 8.95948 17.8802 9.23322 17.9891 9.48V9.54545C18.0928 9.78745 18.265 9.99383 18.4845 10.1392C18.704 10.2846 18.9613 10.3626 19.2245 10.3636H19.3636C19.7976 10.3636 20.2138 10.536 20.5207 10.8429C20.8276 11.1498 21 11.566 21 12C21 12.434 20.8276 12.8502 20.5207 13.1571C20.2138 13.464 19.7976 13.6364 19.3636 13.6364H19.29C19.0267 13.6374 18.7695 13.7154 18.55 13.8608C18.3305 14.0062 18.1583 14.2126 18.0545 14.4545V14.4545Z"/>
                </>
            );
            break;
        case 'submenu':
            return(
                <>
                    <path fill={svgColor} fillRule="evenodd" clipRule="evenodd" d="M6 12C6 13.1046 5.10457 14 4 14C2.89543 14 2 13.1046 2 12C2 10.8954 2.89543 10 4 10C5.10457 10 6 10.8954 6 12ZM14 12C14 13.1046 13.1046 14 12 14C10.8954 14 10 13.1046 10 12C10 10.8954 10.8954 10 12 10C13.1046 10 14 10.8954 14 12ZM20 14C21.1046 14 22 13.1046 22 12C22 10.8954 21.1046 10 20 10C18.8954 10 18 10.8954 18 12C18 13.1046 18.8954 14 20 14Z"/>
                </>
            );
            break;
        case 'BigPacks':
            return(
                <>
                    <path stroke={svgColor} strokeWidth="1.5" d="M4 7.79999H20V17C20 18.1046 19.1046 19 18 19H6C4.89543 19 4 18.1046 4 17V7.79999Z"/>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" d="M7.76471 10.6V6.88235C7.76471 5.84276 6.92195 5 5.88235 5V5C4.84276 5 4 5.84276 4 6.88235V10.6"/>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" d="M20 10.6V6.88235C20 5.84276 19.1572 5 18.1176 5V5C17.078 5 16.2353 5.84276 16.2353 6.88235V10.6"/>
                </>
            );
            break;
        case 'changebox':
            return(
                <>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M14.8519 20.963L19 16.8148L14.8519 12.6667"/>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" d="M19 16.8148C14.3333 16.8148 5 16.8148 5 16.8148"/>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M9.14815 3.37038L5 7.51852L9.14815 11.6667"/>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" d="M5 7.51852C9.66667 7.51852 19 7.51852 19 7.51852"/>

                </>
            );
            break;
        case 'ergebnis1':
            return(
                <>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M11.7966 22C12.1397 22 12.4768 21.9122 12.774 21.7454L19.6158 17.9446C19.9127 17.778 20.1593 17.5384 20.3308 17.2499C20.5024 16.9613 20.5929 16.634 20.5932 16.3008V12.5M11.7966 22C11.4535 22 11.1164 21.9122 10.8192 21.7454L3.9774 17.9446C3.68053 17.778 3.43395 17.5384 3.26239 17.2499C3.09084 16.9613 3.00035 16.634 3 16.3008V8.69922C3.00035 8.36596 3.09084 8.03865 3.26239 7.75013C3.43395 7.46161 3.68053 7.22201 3.9774 7.05538L10.8192 3.2546C11.1164 3.08781 11.4535 3 11.7966 3C12.1397 3 12.4768 3.08781 12.774 3.2546L16.1949 5.15499M11.7966 22L11.7966 12.3061L3.39984 7.65303M13.2308 8.66938L16.1109 11.5041L22 5.83469"/>
                </>
            );
            break;
        case 'ergebnis2':
            return(
                <>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M9.14131 21C9.45889 21 9.77087 20.9122 10.0459 20.7454L13.212 18.845M9.14131 21C8.82373 21 8.51175 20.9122 8.23672 20.7454L1.90459 16.9446C1.62983 16.778 1.40162 16.5384 1.24285 16.2499C1.08408 15.9613 1.00033 15.634 1 15.3008V7.69922C1.00033 7.36596 1.08408 7.03865 1.24285 6.75013C1.40162 6.46161 1.62983 6.22201 1.90459 6.05538L8.23672 2.2546C8.51175 2.08781 8.82373 2 9.14131 2C9.45889 2 9.77087 2.08781 10.0459 2.2546L16.378 6.05538C16.6528 6.22201 16.881 6.46161 17.0398 6.75013C17.1985 7.03865 17.2823 7.36596 17.2826 7.69922V11.5M9.14131 21L9.14131 11.3061M16.9126 6.65303L9.14131 11.3061M9.14131 11.3061L1.37006 6.65303M14.884 14.7561L17.5496 17.5908L23 11.9214"/>
                </>
            );
            break;
        case 'ergebnis3':
            return(
                <>
                    <path fill={svgColor} d="M2 16.3008H1.25L1.25 16.3015L2 16.3008ZM2 8.69922L1.25 8.69847V8.69922H2ZM2.93016 7.05538L2.55206 6.40766L2.54916 6.40937L2.93016 7.05538ZM9.4413 3.2546L9.8194 3.90233L9.82231 3.90062L9.4413 3.2546ZM11.3016 3.2546L10.9206 3.90062L10.9235 3.90232L11.3016 3.2546ZM18.7429 16.3008L19.4929 16.3015V16.3008H18.7429ZM17.8128 17.9446L18.1909 18.5923L18.1938 18.5906L17.8128 17.9446ZM11.3016 21.7454L10.9235 21.0977L10.9206 21.0994L11.3016 21.7454ZM9.4413 21.7454L9.82232 21.0994L9.8194 21.0977L9.4413 21.7454ZM2.93016 17.9446L2.54915 18.5906L2.55207 18.5923L2.93016 17.9446ZM10.3715 12.3061L11.1215 12.3061L11.1215 11.8749L10.7489 11.658L10.3715 12.3061ZM2.75792 7.0049C2.39997 6.79647 1.94082 6.91768 1.73239 7.27563C1.52396 7.63358 1.64517 8.09272 2.00312 8.30115L2.75792 7.0049ZM19.4929 12.5C19.4929 12.0858 19.1571 11.75 18.7429 11.75C18.3287 11.75 17.9929 12.0858 17.9929 12.5H19.4929ZM14.1791 5.80271C14.5368 6.01153 14.9961 5.89082 15.2049 5.53309C15.4137 5.17536 15.293 4.71609 14.9353 4.50727L14.1791 5.80271ZM21.0702 8.87668C20.9648 9.27727 21.2042 9.68741 21.6048 9.79275C22.0054 9.89809 22.4155 9.65873 22.5208 9.25814L21.0702 8.87668ZM23 4.48671L23.7253 4.67744C23.83 4.27951 23.5945 3.87157 23.1975 3.76319L23 4.48671ZM18.7023 2.5358C18.3027 2.4267 17.8903 2.66218 17.7812 3.06176C17.6721 3.46135 17.9076 3.87373 18.3072 3.98283L18.7023 2.5358ZM13.0014 9.49895C12.6444 9.70899 12.5252 10.1687 12.7353 10.5257C12.9453 10.8827 13.405 11.0018 13.762 10.7918L13.0014 9.49895ZM2.75 16.3008V8.69922H1.25V16.3008H2.75ZM2.75 8.69997C2.75021 8.4952 2.80317 8.29495 2.90246 8.11948L1.59697 7.38078C1.36973 7.78236 1.25046 8.23673 1.25 8.69847L2.75 8.69997ZM2.90246 8.11948C3.00169 7.94412 3.14318 7.80048 3.31117 7.7014L2.54916 6.40937C2.1521 6.64355 1.82426 6.97909 1.59697 7.38078L2.90246 8.11948ZM3.30826 7.7031L9.8194 3.90232L9.06321 2.60688L2.55207 6.40766L3.30826 7.7031ZM9.82231 3.90062C9.99035 3.80151 10.1797 3.75 10.3715 3.75V2.25C9.91013 2.25 9.45787 2.37411 9.06029 2.60859L9.82231 3.90062ZM10.3715 3.75C10.5632 3.75 10.7526 3.80151 10.9206 3.90062L11.6826 2.60859C11.2851 2.37411 10.8328 2.25 10.3715 2.25V3.75ZM17.9929 16.3C17.9927 16.5048 17.9398 16.705 17.8405 16.8805L19.146 17.6192C19.3732 17.2176 19.4925 16.7633 19.4929 16.3015L17.9929 16.3ZM17.8405 16.8805C17.7412 17.0559 17.5998 17.1995 17.4318 17.2986L18.1938 18.5906C18.5908 18.3565 18.9187 18.0209 19.146 17.6192L17.8405 16.8805ZM17.4347 17.2969L10.9235 21.0977L11.6797 22.3931L18.1909 18.5923L17.4347 17.2969ZM10.9206 21.0994C10.7526 21.1985 10.5632 21.25 10.3715 21.25V22.75C10.8328 22.75 11.2851 22.6259 11.6826 22.3914L10.9206 21.0994ZM10.3715 21.25C10.1797 21.25 9.99035 21.1985 9.82231 21.0994L9.0603 22.3914C9.45787 22.6259 9.91013 22.75 10.3715 22.75V21.25ZM9.8194 21.0977L3.30826 17.2969L2.55207 18.5923L9.06321 22.3931L9.8194 21.0977ZM3.31117 17.2986C3.14318 17.1995 3.00169 17.0559 2.90246 16.8805L1.59697 17.6192C1.82426 18.0209 2.1521 18.3565 2.54916 18.5906L3.31117 17.2986ZM2.90246 16.8805C2.80317 16.705 2.75021 16.5048 2.75 16.3L1.25 16.3015C1.25046 16.7633 1.36973 17.2176 1.59697 17.6192L2.90246 16.8805ZM10.7489 11.658L2.75792 7.0049L2.00312 8.30115L9.99406 12.9542L10.7489 11.658ZM11.1215 22L11.1215 12.3061L9.62146 12.3061L9.62147 22H11.1215ZM17.9929 12.5V16.3008H19.4929V12.5H17.9929ZM10.9235 3.90232L14.1791 5.80271L14.9353 4.50727L11.6797 2.60688L10.9235 3.90232ZM22.5208 9.25814L23.7253 4.67744L22.2747 4.29598L21.0702 8.87668L22.5208 9.25814ZM23.1975 3.76319L18.7023 2.5358L18.3072 3.98283L22.8024 5.21022L23.1975 3.76319ZM22.6197 3.84028L13.0014 9.49895L13.762 10.7918L23.3803 5.13314L22.6197 3.84028Z"/>
                </>
            );
            break;
        case 'ergebnis4':
            return(
                <>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M18.5 8.39795L10 3.49796M5 9V6.99795C5.00036 6.64722 5.09294 6.30276 5.26846 5.99911C5.44398 5.69546 5.69626 5.44331 6 5.26795L13 1.26795C13.304 1.09241 13.6489 1 14 1C14.3511 1 14.696 1.09241 15 1.26795L22 5.26795C22.3037 5.44331 22.556 5.69546 22.7315 5.99911C22.9071 6.30276 22.9996 6.64722 23 6.99795V14.9979C22.9996 15.3487 22.9071 15.6931 22.7315 15.9968C22.556 16.3004 22.3037 16.5526 22 16.7279L15 20.7279C14.696 20.9035 14.3511 20.9959 14 20.9959C13.6489 20.9959 13.304 20.9035 13 20.7279L12.25 20.2994M5.26999 5.95795L14 11.0079L22.73 5.95795M14 20.7279L14 10.998M2 14L7 11M6 20L11 17M1 19L10 13.5"/>
                </>
            );
            break;
        case 'gabelstapler':
            return(
                <>
                    <path fill={svgColor} d="M3.25 15.5C3.25 15.9142 3.58579 16.25 4 16.25C4.41421 16.25 4.75 15.9142 4.75 15.5H3.25ZM10.92 5.18108L11.563 4.79498V4.79498L10.92 5.18108ZM12.7504 16.0259C12.7647 16.4398 13.1119 16.7638 13.5259 16.7496C13.9398 16.7353 14.2638 16.3881 14.2496 15.9741L12.7504 16.0259ZM16.75 4.5C16.75 4.08579 16.4142 3.75 16 3.75C15.5858 3.75 15.25 4.08579 15.25 4.5H16.75ZM16 16.5H15.25C15.25 16.9142 15.5858 17.25 16 17.25V16.5ZM20.5 17.25C20.9142 17.25 21.25 16.9142 21.25 16.5C21.25 16.0858 20.9142 15.75 20.5 15.75V17.25ZM6 4.75H8.49706V3.25H6V4.75ZM3.25 6V15.5H4.75V6H3.25ZM10.5 16.75H7.5V18.25H10.5V16.75ZM10.277 5.56719C11.343 7.34235 12.5743 10.9189 12.7504 16.0259L14.2496 15.9741C14.0668 10.6774 12.7896 6.83771 11.563 4.79498L10.277 5.56719ZM8.49706 4.75C9.31325 4.75 9.9624 5.04329 10.277 5.56719L11.563 4.79498C10.88 3.6576 9.61866 3.25 8.49706 3.25V4.75ZM6 3.25C4.48122 3.25 3.25 4.48122 3.25 6H4.75C4.75 5.30964 5.30964 4.75 6 4.75V3.25ZM3.26285 6.13822C3.53489 7.58913 4.36739 9.68604 6.00644 11.3296C7.67375 13.0015 10.1508 14.1745 13.593 13.7442L13.407 12.2558C10.4492 12.6255 8.42625 11.6318 7.06856 10.2704C5.68261 8.88063 4.96511 7.07754 4.73715 5.86178L3.26285 6.13822ZM15.25 4.5V16.5H16.75V4.5H15.25ZM16 17.25H20.5V15.75H16V17.25ZM13.25 17.5C13.25 18.1904 12.6904 18.75 12 18.75V20.25C13.5188 20.25 14.75 19.0188 14.75 17.5H13.25ZM12 18.75C11.3096 18.75 10.75 18.1904 10.75 17.5H9.25C9.25 19.0188 10.4812 20.25 12 20.25V18.75ZM10.75 17.5C10.75 16.8096 11.3096 16.25 12 16.25V14.75C10.4812 14.75 9.25 15.9812 9.25 17.5H10.75ZM12 16.25C12.6904 16.25 13.25 16.8096 13.25 17.5H14.75C14.75 15.9812 13.5188 14.75 12 14.75V16.25ZM6.25 17.5C6.25 18.1904 5.69036 18.75 5 18.75V20.25C6.51878 20.25 7.75 19.0188 7.75 17.5H6.25ZM5 18.75C4.30964 18.75 3.75 18.1904 3.75 17.5H2.25C2.25 19.0188 3.48122 20.25 5 20.25V18.75ZM3.75 17.5C3.75 16.8096 4.30964 16.25 5 16.25V14.75C3.48122 14.75 2.25 15.9812 2.25 17.5H3.75ZM5 16.25C5.69036 16.25 6.25 16.8096 6.25 17.5H7.75C7.75 15.9812 6.51878 14.75 5 14.75V16.25Z"/>
                </>
            );
            break;
        case 'gitterbox':
            return(
                <>
                    <rect x="4.67308" y="5.75" width="14.6538" height="11.1923" rx="1.25" stroke={svgColor} strokeWidth="1.5"/>
                    <circle cx="6.23077" cy="18.8462" r="1.15385" fill={svgColor}/>
                    <circle cx="17.7692" cy="18.8462" r="1.15385" fill={svgColor}/>
                    <path d="M9.69231 6.15384V16.5385" stroke={svgColor} strokeWidth="1.5"/>
                    <path d="M14.3077 6.15384V16.5385" stroke={svgColor} strokeWidth="1.5"/>
                    <path d="M18.9231 13.0769L5.07693 13.0769" stroke={svgColor} strokeWidth="1.5"/>
                    <path d="M18.9231 9.61539L5.07693 9.61539" stroke={svgColor} strokeWidth="1.5"/>
                </>
            );
            break;
        case 'LHM_simple':
            return(
                <>
                    <path stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" d="M17.9906 11.7831L21 13.3748V16.5046L18.5378 17.9448M14.7077 20.3517L11.9719 21.9055L3.21739 16.7262V13.3748L5.95318 11.9199M11.9719 15.535C12.2067 15.535 12.4373 15.4731 12.6407 15.3554L17.3219 12.6738C17.525 12.5562 17.6937 12.3872 17.8111 12.1836C17.9285 11.98 17.9904 11.7491 17.9906 11.514V6.15072C17.9904 5.9156 17.9285 5.68466 17.8111 5.4811C17.6937 5.27753 17.525 5.10849 17.3219 4.99092L12.6407 2.3093C12.4373 2.19162 12.2067 2.12967 11.9719 2.12967C11.7371 2.12967 11.5065 2.19162 11.3032 2.3093L6.62193 4.99092C6.4188 5.10849 6.25009 5.27753 6.13271 5.4811C6.01534 5.68466 5.95342 5.9156 5.95318 6.15072V11.514C5.95342 11.7491 6.01534 11.98 6.13271 12.1836C6.25009 12.3872 6.4188 12.5562 6.62193 12.6738L11.3032 15.3554C11.5065 15.4731 11.7371 15.535 11.9719 15.535ZM11.9719 15.535L11.9719 8.69553M17.7171 5.41259L11.9719 8.69553M11.9719 8.69553L6.22676 5.41259"/>
                </>
            );
            break;
        case 'LHM':
            return(
                <>
                    <path d="M12.2455 21.7531V18.5541" stroke={svgColor} strokeWidth="1.5"/>
                    <path d="M21 16.7262V13.3748" stroke={svgColor} strokeWidth="1.5"/>
                    <path d="M12.2455 21.9055L21 16.7261" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M12.2455 18.5542L21 13.3748" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M3.21739 13.3748L6.22676 11.7831" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M3.21739 13.3748V16.5046L5.6796 17.9448V14.8982" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M9.5097 17.1832V20.3517L12.2455 21.9055" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M11.8723 19.2047C12.2316 19.4108 12.6899 19.2867 12.896 18.9274C13.1022 18.5681 12.978 18.1097 12.6187 17.9036L11.8723 19.2047ZM2.84418 14.0254L11.8723 19.2047L12.6187 17.9036L3.59061 12.7243L2.84418 14.0254Z" fill={svgColor}/>
                    <path d="M21 13.3748L18.2642 11.9199" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M18.2642 11.514V6.15072C18.264 5.9156 18.2021 5.68466 18.0847 5.4811C17.9673 5.27753 17.7986 5.10849 17.5955 4.99092L12.9142 2.3093C12.7109 2.19162 12.4803 2.12967 12.2455 2.12967C12.0107 2.12967 11.7801 2.19162 11.5767 2.3093L6.8955 4.99092C6.69238 5.10849 6.52366 5.27753 6.40629 5.4811C6.28891 5.68466 6.22699 5.9156 6.22675 6.15072V11.514C6.22699 11.7491 6.28891 11.98 6.40629 12.1836C6.52366 12.3872 6.69238 12.5562 6.8955 12.6738L11.5767 15.3554C11.7801 15.4731 12.0107 15.535 12.2455 15.535C12.4803 15.535 12.7109 15.4731 12.9142 15.3554L17.5955 12.6738C17.7986 12.5562 17.9673 12.3872 18.0847 12.1836C18.2021 11.98 18.264 11.7491 18.2642 11.514Z" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M6.50034 5.4126L12.2455 8.69554L17.9906 5.4126" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M12.2455 15.535V8.69556" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>

                </>
            );
            break;
        case 'package':
            return(
                <>
                    <path d="M16.5 9.4L8 4.5" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M21 16V7.99999C20.9996 7.64927 20.9071 7.3048 20.7315 7.00116C20.556 6.69751 20.3037 6.44536 20 6.26999L13 2.26999C12.696 2.09446 12.3511 2.00204 12 2.00204C11.6489 2.00204 11.304 2.09446 11 2.26999L4 6.26999C3.69626 6.44536 3.44398 6.69751 3.26846 7.00116C3.09294 7.3048 3.00036 7.64927 3 7.99999V16C3.00036 16.3507 3.09294 16.6952 3.26846 16.9988C3.44398 17.3025 3.69626 17.5546 4 17.73L11 21.73C11.304 21.9055 11.6489 21.9979 12 21.9979C12.3511 21.9979 12.696 21.9055 13 21.73L20 17.73C20.3037 17.5546 20.556 17.3025 20.7315 16.9988C20.9071 16.6952 20.9996 16.3507 21 16Z" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M3.27 6.95999L12 12.01L20.73 6.95999" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M12 22.08V12" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </>
            );
            break;
        case 'palett':
            return(
                <>
                    <path d="M12 19L22 14" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M12 15L22 10" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M2 10L12 5" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M2 10V13.75L5 15.5V12" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M9 14V17.5L12 19" stroke={svgColor} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M11.6646 15.6708C12.0351 15.8561 12.4856 15.7059 12.6708 15.3354C12.8561 14.9649 12.7059 14.5144 12.3354 14.3292L11.6646 15.6708ZM1.66459 10.6708L11.6646 15.6708L12.3354 14.3292L2.33541 9.32918L1.66459 10.6708Z" fill={svgColor}/>
                    <path d="M14.6507 13.6637C15.0172 13.8566 15.4708 13.7159 15.6637 13.3493C15.8566 12.9828 15.7159 12.5292 15.3493 12.3363L14.6507 13.6637ZM5.15069 8.66369L14.6507 13.6637L15.3493 12.3363L5.84931 7.33631L5.15069 8.66369Z" fill={svgColor}/>
                    <path d="M18.1646 12.1708C18.5351 12.3561 18.9856 12.2059 19.1708 11.8354C19.3561 11.4649 19.2059 11.0144 18.8354 10.8292L18.1646 12.1708ZM8.16459 7.17082L18.1646 12.1708L18.8354 10.8292L8.83541 5.82918L8.16459 7.17082Z" fill={svgColor}/>
                    <path d="M21.6646 10.6708C22.0351 10.8561 22.4856 10.7059 22.6708 10.3354C22.8561 9.96493 22.7059 9.51442 22.3354 9.32918L21.6646 10.6708ZM11.6646 5.67082L21.6646 10.6708L22.3354 9.32918L12.3354 4.32918L11.6646 5.67082Z" fill={svgColor}/>
                    <path d="M12 19V15" stroke={svgColor} strokeWidth="1.5"/>
                    <path d="M22 14V10" stroke={svgColor} strokeWidth="1.5"/>
                </>
            );
            break;
        case 'pallet':
            return(
                <>
                   <path fillRule="evenodd" clipRule="evenodd" d="M5 2.25C3.48079 2.25 2.25 3.48079 2.25 5V8.94V14.94V19C2.25 20.5192 3.48079 21.75 5 21.75H9H14.4174C15.283 22.379 16.3482 22.75 17.5 22.75C20.4003 22.75 22.75 20.3991 22.75 17.5C22.75 16.348 22.379 15.2826 21.75 14.4169V8.94V5C21.75 3.48079 20.5192 2.25 19 2.25H15H9H5ZM20.3997 15.1216C20.4107 15.1363 20.4222 15.1506 20.4343 15.1644C20.9449 15.8052 21.25 16.6169 21.25 17.5C21.25 19.5709 19.5717 21.25 17.5 21.25C15.4292 21.25 13.75 19.5708 13.75 17.5C13.75 16.73 14.0021 16.0017 14.426 15.3954C14.437 15.381 14.4474 15.3663 14.4574 15.3511C14.7119 14.9977 15.0252 14.6873 15.381 14.4362C15.4103 14.4188 15.4383 14.3996 15.4649 14.3786C16.058 13.9828 16.7614 13.7494 17.5034 13.75H17.5034C18.6705 13.7509 19.7127 14.2849 20.3997 15.1216ZM20.25 13.0268V9.69H15.75V12.562C16.3015 12.36 16.8929 12.2495 17.5046 12.25C18.5113 12.2508 19.4515 12.5349 20.25 13.0268ZM14.25 13.4109V9.69H9.75V14.19H13.4597C13.6958 13.9043 13.9609 13.6429 14.25 13.4109ZM12.5843 15.69H9.75V20.25H13.0271C12.5343 19.4502 12.25 18.5083 12.25 17.5C12.25 16.8676 12.3686 16.2571 12.5843 15.69ZM3.75 14.19V9.69H8.25V14.19H3.75ZM3.75 8.19H8.25V3.75H5C4.30921 3.75 3.75 4.30921 3.75 5V8.19ZM14.25 8.19H9.75V3.75H14.25V8.19ZM20.25 8.19H15.75V3.75H19C19.6908 3.75 20.25 4.30921 20.25 5V8.19ZM3.75 15.69V19C3.75 19.6908 4.30921 20.25 5 20.25H8.25V15.69H3.75ZM17.5 14.95C17.9142 14.95 18.25 15.2858 18.25 15.7V16.75H19.3C19.7142 16.75 20.05 17.0858 20.05 17.5C20.05 17.9142 19.7142 18.25 19.3 18.25H18.25V19.3C18.25 19.7142 17.9142 20.05 17.5 20.05C17.0858 20.05 16.75 19.7142 16.75 19.3V18.25H15.7C15.2858 18.25 14.95 17.9142 14.95 17.5C14.95 17.0858 15.2858 16.75 15.7 16.75H16.75V15.7C16.75 15.2858 17.0858 14.95 17.5 14.95Z" fill={svgColor}/>
                </>
            );
            break;
        case 'skip':
            return(
                <>
                    <path fillRule="evenodd" clipRule="evenodd" d="M21.6523 10.4697C21.3594 10.1768 20.8846 10.1768 20.5917 10.4697L19.7438 11.3175C19.7479 11.2123 19.75 11.1065 19.75 11C19.75 6.1678 15.8322 2.25001 11 2.25001C6.16779 2.25001 2.25 6.1678 2.25 11C2.25 11.7809 2.36131 12.5285 2.54894 13.2378C2.65487 13.6382 3.06536 13.877 3.4658 13.7711C3.86624 13.6651 4.10499 13.2547 3.99906 12.8542C3.84069 12.2555 3.75 11.6372 3.75 11C3.75 6.99622 6.99621 3.75001 11 3.75001C15.0038 3.75001 18.25 6.99623 18.25 11C18.25 11.1019 18.2477 11.2034 18.2431 11.3044L17.4083 10.4697C17.1154 10.1768 16.6406 10.1768 16.3477 10.4697C16.0548 10.7626 16.0548 11.2374 16.3477 11.5303L18.2118 13.3945C18.2276 13.4118 18.2442 13.4284 18.2615 13.4442L18.4697 13.6523C18.7626 13.9452 19.2374 13.9452 19.5303 13.6523L21.6523 11.5303C21.9452 11.2374 21.9452 10.7626 21.6523 10.4697ZM11.6313 22.2571C11.3384 22.55 10.8635 22.55 10.5706 22.2571L5.54925 17.2357C5.53266 17.2218 5.51654 17.2071 5.50092 17.1916C5.36815 17.0594 5.2952 16.8899 5.28214 16.7167C5.26488 16.5049 5.33727 16.2872 5.49931 16.1251L6.5381 15.0863L7.61843 14.0009C7.63647 13.9828 7.65521 13.9658 7.67457 13.9499L10.5692 11.0552C10.8621 10.7623 11.337 10.7623 11.6299 11.0552L16.7012 16.1266C16.9941 16.4194 16.9941 16.8943 16.7012 17.1872L11.6313 22.2571ZM7.60439 16.1414L11.0995 12.6462L15.1102 16.6569L11.101 20.6661L7.0915 16.6567L7.60439 16.1414Z" fill={svgColor}/>
                </>
            );
            break;
        case 'weight':
            return(
                <>
                    <circle cx="12" cy="5.35294" r="2.35294" stroke={svgColor} strokeWidth="1.5"/>
                    <path d="M5.72582 9.38323C5.88392 8.41603 6.71958 7.70587 7.69962 7.70587H16.3004C17.2804 7.70587 18.1161 8.41603 18.2742 9.38323L19.6203 17.6185C19.8193 18.8357 18.8799 19.9412 17.6465 19.9412H6.35347C5.12011 19.9412 4.1807 18.8357 4.37966 17.6185L5.72582 9.38323Z" stroke={svgColor} strokeWidth="1.5"/>
                </>
            );
            break;
    
        default:
            break;
    }
};
    
export default SvgPath;