import { queryOptions } from '@tanstack/react-query'
import { $api } from '../client'
import type { USER_RESPONSE_DTO } from './DTOs'

const originalUsersQueryOptions = $api.queryOptions('get', '/api/users', {
  queryKey: ['users'],
})

export const usersQueryOptions = queryOptions({
  ...originalUsersQueryOptions,
  select: (data): USER_RESPONSE_DTO => {
    return Array.isArray(data) ? data : ([data] as USER_RESPONSE_DTO)
  },
})

export const userQueryOptions = (userRef: number) =>
  $api.queryOptions('get', '/api/users/{userRef}', {
    queryKey: ['users', userRef],
    params: {
      path: {
        userRef,
      },
    },
  })

export const userRechteQueryOptions = (userRef: number) =>
  $api.queryOptions('get', '/api/users/{userRef}/acos', {
    queryKey: ['rechte', userRef],
    params: {
      path: {
        userRef,
      },
    },
  })

export const userGroupsRechteQueryOptions = (userRef: number) =>
  $api.queryOptions('get', '/api/users/{userRef}/groups/acos', {
    queryKey: ['gruppen-rechte', userRef],
    params: {
      path: {
        userRef,
      },
    },
  })
