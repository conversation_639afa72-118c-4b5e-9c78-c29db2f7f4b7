/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/api/login": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Login
         * @description Liefert einen JWT
         */
        post: operations["Login"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * <PERSON><PERSON>
         * @description Liefert eine gefilterte Liste von Benutzern.
         */
        get: operations["GetUsers"];
        /**
         * Bestehenden Benutzer aktualisieren.
         * @description Aktualisiert einen bestehenden SYS_BEN-Datensatz.
         */
        put: operations["UpdateUser"];
        /**
         * Neuen Benutzer anlegen
         * @description Legt einen neuen SYS_BEN-Datensatz an.
         */
        post: operations["CreateUser"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/users/{userRef}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Ein Benutzer
         * @description Liefert einen einzelnen Benutzer anhand der Ref-ID.
         */
        get: operations["GetUserByRef"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/users/{userRef}/groups": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Alle Rechtegruppen eines Benutzers
         * @description Liefert die einem Benutzer zugewiesenen Gruppen.
         */
        get: operations["GetUserGroupsByRef"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/users/{userRef}/groups/acos": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Alle ACOs aus Rechtegruppen eines Benutzers
         * @description Liefert alle ACOs aller Gruppen, die dem Benutzer zugeordneten sind.
         */
        get: operations["GetUserGroupsAcos"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/users/{userRef}/acos": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Alle einzelnen ACOs eines Benutzers
         * @description Liefert alle ACOs ohne Gruppenzuordnung, die dem Benutzer zugeordneten sind.
         */
        get: operations["SysAcoForUser"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/users/me/permissions": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Meine Rechte
         * @description Liefert alle dem angemeldeten Benutzer zugewiesenen ACOs.
         */
        get: operations["GetGrantedAcos"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/users/exists/{userId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Überprüfen, ob ein Benutzer existiert
         * @description Prüft, ob ein Benutzer mit der angegebenen userId bereits existiert.
         */
        get: operations["CheckUserExists"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/groups": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Alle Rechtegruppen
         * @description Liefert die Liste der Rechtegruppen.
         */
        get: operations["GetUserGroups"];
        put?: never;
        /**
         * Neue Rechtegruppe anlegen
         * @description Legt einen neuen SYS_BEN_GRP-Datensatz an.
         */
        post: operations["CreateUserGroup"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/groups/{groupRef}/users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Alle Benutzer einer Rechtegruppen
         * @description Liefert alle Benutzer des Systems, die mit der Rechtegruppe verknüpft sind.
         */
        get: operations["GetUsersForSysBenGrp"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/groups/{groupRef}/acos": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Alle ACOs einer Rechtegruppen
         * @description Liefert alle ACOs des Systems, die mit der Rechtegruppe verknüpft sind.
         */
        get: operations["GetGrpAcos"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/acos": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Alle ACOs
         * @description Liefert alle ACOs des Systems.
         */
        get: operations["GetSysAcos"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/acos/types": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Alle ACO-Typen
         * @description Liefert alle verfügbaren Typen der ACOs des Systems.
         */
        get: operations["GetSysAcoTypes"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/acos/{acoRef}/users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Alle Benutzer mit diesem ACO
         * @description Liefert alle Benutzer des Systems, die mit dem ACO verknüpft sind.
         */
        get: operations["GetUsersForSysAco"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/acos/{acoRef}/groups": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Alle Rechtegruppen mit diesem ACO
         * @description Liefert alle Rechtegruppen des Systems, die mit dem ACO verknüpft sind.
         */
        get: operations["GetGroupsForSysAco"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/acos/objectgroups": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Alle Objektgruppen der ACOs
         * @description Liefert alle Objektgruppen der ACOs.
         */
        get: operations["GetObjectGroups"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/companies": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Alle Firmen
         * @description Liefert die Liste aktiver Firmen.
         */
        get: operations["GetSysFirma"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/locations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Alle Standorte
         * @description Liefert die Liste aller Standorte.
         */
        get: operations["GetPcdLocations"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        CreateGroupDto: {
            groupArt: string;
            groupId: string;
            groupName: string | null;
            /** Format: int32 */
            locationReference: number | null;
            isAdmin: boolean;
        };
        CreateUserDto: {
            id: string;
            name: string;
            shortName: string;
            numberId: string;
            title: string;
            email: string;
            /** Format: int32 */
            companyRef: number;
            isAdmin: boolean;
            password: string;
        };
        GrantedAcoView: {
            /** Format: int32 */
            ref?: number;
            application?: string | null;
            objectGroup?: string | null;
            objectName?: string | null;
            /** Format: int32 */
            objectId?: number | null;
            name?: string | null;
            typ?: string | null;
            beschreibung?: string | null;
        };
        GroupsForSysAcoResult: {
            /** Format: int32 */
            refGrp?: number | null;
            /** Format: int32 */
            refAco?: number;
            groupName?: string | null;
            groupId?: string | null;
            rechteFlags?: string | null;
        };
        GrpAcoView: {
            /** Format: int32 */
            ref?: number;
            /** Format: int32 */
            refAco?: number;
            /** Format: int32 */
            refGrp?: number;
            grpName?: string;
            beschreibung?: string | null;
            acoName?: string | null;
            rechteFlags?: string | null;
        };
        PcdLocationView: {
            /** Format: int32 */
            ref?: number;
            name: string;
            beschreibung?: string | null;
        };
        SysAcoForUserResult: {
            /** Format: int32 */
            refAco?: number;
            beschreibung?: string | null;
            name?: string | null;
            rechteFlags?: string | null;
            /** Format: int32 */
            refGrp?: number | null;
            typ?: string | null;
        };
        SysBenGrpView: {
            /** Format: int32 */
            ref?: number;
            groupArt?: string | null;
            groupName?: string | null;
            groupId?: string | null;
            /** Format: int32 */
            refAco?: number | null;
            /** Format: int32 */
            refLocation?: number | null;
            adminFlag?: boolean;
            /** Format: int32 */
            reihenfolge?: number | null;
            defaultGroup?: boolean;
        };
        SysBenView: {
            /** Format: int32 */
            ref?: number;
            /** Format: int32 */
            refAco?: number | null;
            /** Format: int32 */
            refFirma?: number | null;
            status?: string | null;
            isAdmin?: boolean;
            userName?: string | null;
            userShortName?: string | null;
            userId?: string | null;
            userNumId?: string | null;
            userTitle?: string | null;
            email?: string | null;
            /** Format: date-time */
            createDate?: string | null;
            /** Format: date-time */
            lastLogin?: string | null;
            station?: string | null;
            sprache?: string | null;
            /** Format: date-time */
            lastPasswordSet?: string | null;
            resetPassword?: boolean;
            firma?: string | null;
            /** Format: int32 */
            lastRefMand?: number | null;
            /** Format: int32 */
            lastRefLocation?: number | null;
        };
        SysFirmaView: {
            /** Format: int32 */
            ref?: number;
            /** Format: int32 */
            refAdminGrp?: number | null;
            status?: string | null;
            name?: string | null;
            beschreibung?: string | null;
            /** Format: int32 */
            maxSessions?: number | null;
            /** Format: int32 */
            refMasterFirma?: number | null;
        };
        TokenResponseDto: {
            accessToken: string;
        };
        UpdateUserDto: {
            /** Format: int32 */
            userRef: number;
            id: string;
            name: string;
            shortName: string;
            numberId: string;
            title: string;
            email: string;
            /** Format: int32 */
            companyRef: number;
            isAdmin: boolean;
            password: string;
        };
        UserAcoView: {
            /** Format: int32 */
            ref?: number;
            /** Format: int32 */
            refAco?: number;
            /** Format: int32 */
            refBen?: number;
            /** Format: int32 */
            refGrp?: number;
            grpName?: string | null;
            beschreibung?: string | null;
            acoName?: string | null;
            rechteFlags?: string | null;
        };
        UserCredentialsDto: {
            username?: string;
            userPrefix?: string;
            password?: string;
            server?: string;
            schema?: string;
        };
        UsersForSysAcoResult: {
            /** Format: int32 */
            refBen?: number;
            /** Format: int32 */
            refAco?: number;
            userName?: string | null;
            userId?: string | null;
            rechteFlags?: string | null;
        };
        UsersForSysBenGrpResult: {
            /** Format: int32 */
            refBen?: number;
            /** Format: int32 */
            refGrp?: number | null;
            userName?: string | null;
            userId?: string | null;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    Login: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UserCredentialsDto"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TokenResponseDto"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    GetUsers: {
        parameters: {
            query?: {
                onlyActive?: boolean;
                includeAdmins?: boolean;
                firmaId?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SysBenView"][];
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
    UpdateUser: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateUserDto"];
            };
        };
        responses: {
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    CreateUser: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateUserDto"];
            };
        };
        responses: {
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    GetUserByRef: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                userRef: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SysBenView"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    GetUserGroupsByRef: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                userRef: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SysBenGrpView"][];
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    GetUserGroupsAcos: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                userRef: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserAcoView"][];
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    SysAcoForUser: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                userRef: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SysAcoForUserResult"][];
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    GetGrantedAcos: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GrantedAcoView"][];
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
    CheckUserExists: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                userId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": boolean;
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    GetUserGroups: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SysBenGrpView"][];
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
    CreateUserGroup: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateGroupDto"];
            };
        };
        responses: {
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    GetUsersForSysBenGrp: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                groupRef: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UsersForSysBenGrpResult"][];
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    GetGrpAcos: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                groupRef: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GrpAcoView"][];
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    GetSysAcos: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GrantedAcoView"][];
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
    GetSysAcoTypes: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string[];
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
    GetUsersForSysAco: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                acoRef: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UsersForSysAcoResult"][];
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    GetGroupsForSysAco: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                acoRef: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["GroupsForSysAcoResult"][];
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    GetObjectGroups: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string[];
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
    GetSysFirma: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SysFirmaView"][];
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
    GetPcdLocations: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PcdLocationView"][];
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
}
