import { Checkbox } from "@/components/ui/checkbox"
import type { GROUP_RESPONSE_DTO } from "@/lib/backend/groups/DTOs"
import { useDraggable, type UniqueIdentifier } from "@dnd-kit/core"

interface DraggableGroupCardProps {
  group: GROUP_RESPONSE_DTO[number]
  isDragOverlay?: boolean
  isSelected: boolean
  onToggleSelection: () => void
}

export const DraggableGroupCard = ({
  group,
  isDragOverlay = false,
  isSelected,
  onToggleSelection,
}: DraggableGroupCardProps) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id: group.ref as UniqueIdentifier,
      data: {
        type: 'group',
        group,
      },
    })

  const style = transform
    ? {
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
      }
    : undefined

  const getCardStyles = () => {
    let baseStyles =
      'p-4 bg-background border rounded-xl cursor-grab transition-all duration-200 select-none flex items-center gap-3 group '

    if (isDragOverlay) {
      baseStyles +=
        'shadow-2xl border-primary rotate-2 scale-105 ring-2 ring-primary/20 '
    } else if (isDragging) {
      baseStyles += 'opacity-30 '
    } else if (isSelected) {
      baseStyles +=
        'border-primary bg-primary/5 shadow-md ring-1 ring-primary/20 '
    } else {
      baseStyles +=
        'hover:shadow-md hover:border-primary/50 hover:bg-accent/50 '
    }

    return baseStyles
  }

  const handleCheckboxChange = (e: React.MouseEvent) => {
    e.stopPropagation()
    onToggleSelection()
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={getCardStyles()}
      tabIndex={0}
      role="button"
      aria-label={`Gruppe ${group.groupName} verschieben`}
    >
      {!isDragOverlay && (
        <div onClick={handleCheckboxChange} className="flex-shrink-0">
          <Checkbox
            checked={isSelected}
            onChange={() => {}}
            className="pointer-events-none"
          />
        </div>
      )}

      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-semibold text-foreground group-hover:text-primary transition-colors">
          {group.groupName || 'Unbenannte Gruppe'}
        </h4>
        <div className="flex items-center gap-2 mt-1">
          <p className="text-xs text-muted-foreground">
            {group.groupArt || 'Kein Typ'}
          </p>
          {group.groupId && (
            <>
              <span className="text-xs text-muted-foreground/50">•</span>
              <p className="text-xs text-muted-foreground font-mono">
                ID: {group.groupId}
              </p>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
