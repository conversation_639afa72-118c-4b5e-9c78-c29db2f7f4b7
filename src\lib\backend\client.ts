import createFetchClient, { type Middleware } from 'openapi-fetch'
import createClient from 'openapi-react-query'
import type { paths } from 'schema'
import { getCookieValue, setCookieValue } from '../utils'
import { login } from './auth/functions'

const authMiddleware: Middleware = {
  async onRequest({ request }) {
    if (!getCookieValue('Token')) {
      try {
        const isDev = import.meta.env.DEV

        const authRes = await login({
          username: isDev
            ? import.meta.env.VITE_API_USERNAME
            : getCookieValue('username'),
          password: isDev
            ? import.meta.env.VITE_API_PASSWORD
            : getCookieValue('password'),
          schema: isDev
            ? import.meta.env.VITE_API_SCHEMA
            : getCookieValue('schema'),
          server: isDev
            ? import.meta.env.VITE_API_SERVER
            : getCookieValue('server'),
          userPrefix: isDev
            ? import.meta.env.VITE_API_USERPREFIX
            : getCookieValue('userPrefix'),
        })

        if (authRes?.accessToken) {
          setCookieValue('Token', authRes.accessToken)
        } else {
          throw new Error('Kein Access Token erhalten')
        }
      } catch (error) {
        console.error('Authentifizierung fehlgeschlagen:', error)
        throw error
      }
    }

    request.headers.set('Authorization', `Bearer ${getCookieValue('Token')}`)
    return request
  },
}

export const fetchClient = createFetchClient<paths>({
  baseUrl: `${import.meta.env.VITE_API_URL}`,
})
fetchClient.use(authMiddleware)

export const $api = createClient(fetchClient)
