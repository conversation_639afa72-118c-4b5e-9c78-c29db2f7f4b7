import { Button } from '@/components/ui/button'
import type { USER_RESPONSE_DTO } from '@/lib/backend/user/DTOs'
import { cn } from '@/lib/utils'
import { useDroppable } from '@dnd-kit/core'
import { UserMinus, Users } from 'lucide-react'
import { DraggableUserCard } from './group-dialog-draggable-user-card'

interface UserContainerProps {
  id: string
  title: string
  icon: React.ReactNode
  users: USER_RESPONSE_DTO
  isDropTarget: boolean
  isEmpty: boolean
  selectedUsers: Set<number>
  onToggleSelection: (userRef: number) => void
  onSelectAll: () => void
  onClearSelection: () => void
}

export const UserContainer = ({
  id,
  title,
  icon,
  users,
  isDropTarget,
  isEmpty,
  selectedUsers,
  onToggleSelection,
  onSelectAll,
  onClearSelection,
}: UserContainerProps) => {
  const { isOver, setNodeRef } = useDroppable({
    id,
    data: {
      type: 'container',
    },
  })

  const getContainerStyles = () => {
    return cn(
      'min-h-96 max-h-[500px] p-6 overflow-y-auto overflow-x-hidden rounded-2xl transition-all duration-300 border',
      {
        'border-primary/50 bg-primary/5 shadow-lg ring-2 ring-primary/20':
          isOver || isDropTarget,
        'border-border/50 bg-background': !(isOver || isDropTarget),
      },
    )
  }

  const allSelected =
    users.length > 0 &&
    users.every((u) => u.ref !== undefined && selectedUsers.has(u.ref))
  const someSelected = users.some(
    (u) => u.ref !== undefined && selectedUsers.has(u.ref),
  )

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {icon}
          <h3 className="text-lg font-semibold text-foreground">{title}</h3>
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-muted text-muted-foreground">
            {users.length}
          </span>
        </div>

        {users.length > 0 && (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={allSelected ? onClearSelection : onSelectAll}
              className="text-xs"
            >
              {allSelected ? 'Alle abwählen' : 'Alle auswählen'}
            </Button>
          </div>
        )}
      </div>

      <div ref={setNodeRef} className={getContainerStyles()}>
        <div className="space-y-3">
          {users
            .filter((user) => user.ref !== undefined)
            .map((user) => (
              <DraggableUserCard
                key={user.ref}
                user={user}
                isSelected={selectedUsers.has(user.ref as number)}
                onToggleSelection={() => onToggleSelection(user.ref as number)}
              />
            ))}

          {isEmpty && (
            <div className="text-center py-12 space-y-3">
              <div className="w-16 h-16 mx-auto rounded-full bg-muted/50 flex items-center justify-center">
                {id === 'assigned' ? (
                  <UserMinus className="w-8 h-8 text-muted-foreground/50" />
                ) : (
                  <Users className="w-8 h-8 text-muted-foreground/50" />
                )}
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  {isDropTarget
                    ? `Hier ablegen zum ${id === 'assigned' ? 'Zuweisen' : 'Entfernen'}`
                    : `Keine Benutzer ${id === 'assigned' ? 'zugewiesen' : 'verfügbar'}`}
                </p>
                {isDropTarget && (
                  <p className="text-xs text-muted-foreground/60">
                    Benutzer hierher ziehen
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
