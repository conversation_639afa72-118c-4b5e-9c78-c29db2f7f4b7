import { Button } from '@/components/ui/button'
import type { GROUP_RESPONSE_DTO } from '@/lib/backend/groups/DTOs'
import { useDroppable } from '@dnd-kit/core'
import { UserMinus, Users } from 'lucide-react'
import { DraggableGroupCard } from './user-dialog-draggable-group-card'

interface GroupContainerProps {
  id: string
  title: string
  icon: React.ReactNode
  groups: GROUP_RESPONSE_DTO
  isDropTarget: boolean
  isEmpty: boolean
  selectedGroups: Set<number>
  onToggleSelection: (groupRef: number) => void
  onSelectAll: () => void
  onClearSelection: () => void
}

export const GroupContainer = ({
  id,
  title,
  icon,
  groups,
  isDropTarget,
  isEmpty,
  selectedGroups,
  onToggleSelection,
  onSelectAll,
  onClearSelection,
}: GroupContainerProps) => {
  const { isOver, setNodeRef } = useDroppable({
    id,
    data: {
      type: 'container',
    },
  })

  const getContainerStyles = () => {
    let baseStyles =
      'min-h-96 max-h-[500px] p-6 overflow-y-auto overflow-x-hidden rounded-2xl transition-all duration-300 border '

    if (isOver || isDropTarget) {
      baseStyles +=
        'border-primary/50 bg-primary/5 shadow-lg ring-2 ring-primary/20 '
    } else {
      baseStyles += 'border-border/50 bg-background '
    }

    return baseStyles
  }

  const allSelected =
    groups.length > 0 &&
    groups.every((g) => g.ref !== undefined && selectedGroups.has(g.ref))
  const someSelected = groups.some(
    (g) => g.ref !== undefined && selectedGroups.has(g.ref),
  )

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {icon}
          <h3 className="text-lg font-semibold text-foreground">{title}</h3>
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-muted text-muted-foreground">
            {groups.length}
          </span>
        </div>

        {groups.length > 0 && (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={allSelected ? onClearSelection : onSelectAll}
              className="text-xs"
            >
              {allSelected ? 'Alle abwählen' : 'Alle auswählen'}
            </Button>
          </div>
        )}
      </div>

      <div ref={setNodeRef} className={getContainerStyles()}>
        <div className="space-y-3">
          {groups
            .filter((group) => group.ref !== undefined)
            .map((group) => (
              <DraggableGroupCard
                key={group.ref}
                group={group}
                isSelected={selectedGroups.has(group.ref as number)}
                onToggleSelection={() => onToggleSelection(group.ref as number)}
              />
            ))}

          {isEmpty && (
            <div className="text-center py-12 space-y-3">
              <div className="w-16 h-16 mx-auto rounded-full bg-muted/50 flex items-center justify-center">
                {id === 'assigned' ? (
                  <UserMinus className="w-8 h-8 text-muted-foreground/50" />
                ) : (
                  <Users className="w-8 h-8 text-muted-foreground/50" />
                )}
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  {isDropTarget
                    ? `Hier ablegen zum ${id === 'assigned' ? 'Zuweisen' : 'Entfernen'}`
                    : `Keine Gruppen ${id === 'assigned' ? 'zugewiesen' : 'verfügbar'}`}
                </p>
                {isDropTarget && (
                  <p className="text-xs text-muted-foreground/60">
                    Gruppe hierher ziehen
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
