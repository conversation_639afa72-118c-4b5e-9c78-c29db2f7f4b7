import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useLocations } from '@/lib/backend/locations/hooks'
import { useState } from 'react'

export const GroupDialogLocationSelect = ({
  defaultValue,
}: {
  defaultValue: string | number | null | undefined
}) => {
  const { data: locations } = useLocations()
  const [value, setValue] = useState(defaultValue?.toString() ?? '')
  return (
    <Select value={value} onValueChange={setValue} defaultValue={defaultValue?.toString() ?? ''}>
      <SelectTrigger>
        <SelectValue
          placeholder="Niederlassung"
        />
      </SelectTrigger>
      <SelectContent>
        {locations?.map((location) => (
          <SelectItem key={location.ref} value={location.ref?.toString() || ''}>
            {location.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
