import type { RECHTE_RESPONSE_DTO } from '@/lib/backend/rechte/DTOs'
import type { USER_GROUPS_RECHTE_RESPONSE_DTO } from '@/lib/backend/user/DTOs'
import type { PermissionData } from './useData'

type permissionButtonType = 'R' | 'W' | 'E' | 'A' | 'G'

export function buildHierarchy(
  permissions: RECHTE_RESPONSE_DTO,
): Array<RECHTE_RESPONSE_DTO[number] & { children?: RECHTE_RESPONSE_DTO }> {
  if (!permissions?.length) {
    return []
  }

  return permissions.map((permission) => ({
    ...permission,
    children: [],
  }))
}

export function getTypeBadgeColor(type: string): string {
  switch (type.toUpperCase()) {
    case 'ACO':
      return 'bg-orange-100 text-orange-700 border-orange-200'
    case 'ARTIKEL':
      return 'bg-blue-100 text-blue-700 border-blue-200'
    case 'MENU':
      return 'bg-green-100 text-green-700 border-green-200'
    case 'BUTTON':
      return 'bg-purple-100 text-purple-700 border-purple-200'
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200'
  }
}

export function getUserPermissionForAco(
  userPermissions: PermissionData,
  acoRef: string | number,
): string {
  if (!userPermissions || !Array.isArray(userPermissions)) {
    return ''
  }

  const userPermission = userPermissions.find(
    (perm) => perm.refAco?.toString() === acoRef.toString(),
  )

  return userPermission?.rechteFlags || ''
}

export function hasPermission(
  userPermissions: PermissionData,
  acoRef: string | number,
  permissionType: permissionButtonType,
): boolean {
  const flags = getUserPermissionForAco(userPermissions, acoRef)
  return flags.includes(permissionType)
}

export function getGroupPermissionsForAco(
  groupPermissions: USER_GROUPS_RECHTE_RESPONSE_DTO,
  acoRef: string | number,
): Array<{ flags: string; groupName?: string | null; groupRef?: number }> {
  return groupPermissions
    .filter((perm) => perm.refAco?.toString() === acoRef.toString())
    .map((perm) => ({
      flags: perm.rechteFlags || '',
      groupName: perm.grpName,
      groupRef: perm.refGrp,
    }))
}

export function hasGroupPermission(
  groupPermissions: USER_GROUPS_RECHTE_RESPONSE_DTO,
  acoRef: string | number,
  permissionType: permissionButtonType,
): { hasPermission: boolean; groupName?: string } {
  const groupPerms = getGroupPermissionsForAco(groupPermissions, acoRef)

  for (const groupPerm of groupPerms) {
    if (groupPerm.flags.includes(permissionType)) {
      return {
        hasPermission: true,
        groupName: groupPerm.groupName!,
      }
    }
  }

  return { hasPermission: false }
}

export function getCombinedPermissionInfo(
  userPermissions: PermissionData,
  groupPermissions: USER_GROUPS_RECHTE_RESPONSE_DTO,
  acoRef: string | number,
  permissionType: permissionButtonType,
): {
  hasPermission: boolean
  source: 'user' | 'group' | 'none'
  groupName?: string
} {
  const hasUserPerm = hasPermission(userPermissions, acoRef, permissionType)

  if (hasUserPerm) {
    return { hasPermission: true, source: 'user' }
  }

  const groupPermResult = hasGroupPermission(
    groupPermissions,
    acoRef,
    permissionType,
  )
  if (groupPermResult.hasPermission) {
    return {
      hasPermission: true,
      source: 'group',
      groupName: groupPermResult.groupName,
    }
  }

  return { hasPermission: false, source: 'none' }
}
