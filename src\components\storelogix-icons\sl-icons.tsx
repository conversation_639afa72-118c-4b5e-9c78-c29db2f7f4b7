"use client"

import React from 'react';

import SvgPath from './svgPath';
import type { iconProps } from './sl-icons.types';

const StorelogixIcon:React.FC<iconProps> = ({
    name,
    color = 'black',
    width = '24',
    height = '24',
    size,
    viewBox = '0 0 24 24'
}) => {

    return (
        <svg
            xmlns='http://www.w3.org/2000/svg'
            width={size || width}
            height={size || height}
            viewBox={viewBox}
            fill='none'
        >    
            <defs>
                <linearGradient id="kiwi" gradientUnits="userSpaceOnUse" gradientTransform='rotate(90)'>
                    <stop offset={'0'} stopColor={'#57DD00'}/>
                    <stop offset={'1'} stopColor={'#CAE114'}/>
                </linearGradient>
                <linearGradient id="berry" gradientUnits="userSpaceOnUse" gradientTransform='rotate(90)'>
                    <stop offset={'0'} stopColor={'#a72dff'}/>
                    <stop offset={'1'} stopColor={'#f13cff'}/>
                </linearGradient>
                <linearGradient id="beach" gradientUnits="userSpaceOnUse" gradientTransform='rotate(90)'>
                    <stop offset={'0'} stopColor={'#f49136'}/>
                    <stop offset={'1'} stopColor={'#fbd600'}/>
                </linearGradient>
            </defs>
            <SvgPath name={name} color={color} />
        </svg>
        );
    };
    
export default StorelogixIcon;