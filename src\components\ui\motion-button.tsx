import { motion } from "motion/react";
import { forwardRef } from "react";
import { Button } from "./button";
import type { ComponentProps} from "react";
import type { MotionProps } from "motion/react";

type ButtonProps = ComponentProps<typeof Button>;
type MotionButtonProps = ButtonProps & MotionProps;

const MotionButton = forwardRef<HTMLButtonElement, MotionButtonProps>(
  ({ children, ...props }, ref) => {
    const MotionButtonComponent = motion(Button);

    return (
      <MotionButtonComponent 
        ref={ref} 
        {...props}
        initial={{ 
          scale: 0.96,
          opacity: 0.8
        }}
        animate={{ 
          scale: 1,
          opacity: 1
        }}
        whileHover={{ 
          scale: 1.04,
          y: -1,
          boxShadow: "0 8px 25px rgba(0, 0, 0, 0.15)"
        }}
        whileTap={{ 
          scale: 0.96,
          y: 0
        }}
        whileFocus={{
          scale: 1.02,
          boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.3)"
        }}
        transition={{
          type: "spring",
          stiffness: 320,
          damping: 20,
          mass: 0.6,
          duration: 0.3
        }}
        style={{
          transformOrigin: "center"
        }}
      >
        {children}
      </MotionButtonComponent>
    );
  }
);

MotionButton.displayName = "MotionButton";

export default MotionButton;