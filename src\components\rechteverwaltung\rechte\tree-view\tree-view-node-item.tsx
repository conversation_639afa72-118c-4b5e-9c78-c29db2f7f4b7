import {
  ChevronDownIcon,
  ChevronRightIcon,
  EyeIcon,
  UsersIcon,
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuTrigger,
} from '@/components/ui/context-menu'
import { CustomContextMenuItem } from '@/components/ui/context-menu-item'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import type { RECHTE_RESPONSE_DTO } from '@/lib/backend/rechte/DTOs'
import type {
  USER_GROUPS_RECHTE_RESPONSE_DTO,
  USER_RECHTE_RESPONSE_DTO,
} from '@/lib/backend/user/DTOs'
import { cn } from '@/lib/utils'
import { useState } from 'react'

import { DetailsDialog } from '../dialog/details-dialog'
import { RechteUserDialog } from '../dialog/rechte-user-dialog'
import { permission_buttons, PERMISSION_KEY_TO_FLAG } from './constants'
import { getCombinedPermissionInfo, getTypeBadgeColor } from './utils'
import type { PermissionData } from './useData'

type TreeNodeItemProps = {
  permission: RECHTE_RESPONSE_DTO[number]
  userPermissions: PermissionData
  groupPermissions: USER_GROUPS_RECHTE_RESPONSE_DTO
  onPermissionChange?: (
    permission: RECHTE_RESPONSE_DTO[number],
    right: string,
    action: string,
  ) => void
  showChevron?: boolean
  isExpanded?: boolean
  onToggle?: () => void
  hasChildren?: boolean
}

export const TreeNodeItem = ({
  permission,
  userPermissions,
  groupPermissions,
  onPermissionChange,
  showChevron = false,
  isExpanded = false,
  onToggle,
  hasChildren = false,
}: TreeNodeItemProps) => {
  const [showDetailsDialog, setShowDetailsDialog] = useState(false)
  const [showUserDialog, setShowUserDialog] = useState(false)

  return (
    <>
      <ContextMenu>
        <ContextMenuTrigger asChild>
          <div className="flex items-center justify-between w-full pl-4">
            <div className="flex items-center gap-2 flex-1 min-w-0">
              {showChevron && hasChildren && (
                <button
                  onClick={onToggle}
                  className="p-1 hover:bg-gray-100 rounded"
                >
                  {isExpanded ? (
                    <ChevronDownIcon className="w-4 h-4 text-gray-600" />
                  ) : (
                    <ChevronRightIcon className="w-4 h-4 text-gray-600" />
                  )}
                </button>
              )}
              {!showChevron && hasChildren && <div className="w-6" />}

              {permission.typ && (
                <Tooltip>
                  <TooltipTrigger>
                    <div
                      className={`text-xs font-medium flex items-center gap-1 rounded-md p-1 ${getTypeBadgeColor(permission.typ)}`}
                    >
                      {permission.typ}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{permission.typ}</p>
                  </TooltipContent>
                </Tooltip>
              )}
              <div className="flex flex-col gap-1">
                <span className="text-slate-700 truncate text-sm font-bold">
                  {permission.beschreibung}
                </span>
                <span className="text-slate-700 truncate text-sm">
                  {permission.name}
                </span>
              </div>
            </div>

            <div className="flex items-center gap-1 ml-4">
              {permission_buttons.map((btn) => {
                const permissionFlag = PERMISSION_KEY_TO_FLAG[btn.key]
                const permissionInfo = getCombinedPermissionInfo(
                  userPermissions,
                  groupPermissions,
                  permission.ref || 0,
                  permissionFlag!,
                )

                const getButtonStyle = () => {
                  if (!permissionInfo.hasPermission) {
                    return 'border-gray-300 bg-white hover:bg-gray-50 text-gray-600'
                  }

                  if (permissionInfo.source === 'user') {
                    return 'bg-primary-gradient text-primary-foreground border-primary/40 shadow-sm hover:bg-primary-gradient-hover'
                  }

                  return 'bg-secondary-gradient text-secondary-foreground border-secondary/40 shadow-sm hover:bg-secondary-gradient-hover'
                }

                const getTooltipText = () => {
                  if (!permissionInfo.hasPermission) {
                    return `${btn.title} (inaktiv)`
                  }

                  if (permissionInfo.source === 'user') {
                    return `${btn.title} (User-Recht)`
                  }

                  return `${btn.title} (Gruppe: ${permissionInfo.groupName || 'Unbekannt'})`
                }

                return (
                  <Tooltip key={btn.key}>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className={cn(
                          'w-6 h-6 p-0 text-xs font-medium border transition-all duration-200',
                          getButtonStyle(),
                        )}
                      >
                        {btn.label}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{getTooltipText()}</p>
                    </TooltipContent>
                  </Tooltip>
                )
              })}
            </div>
            <div className="border-l border-red-800 h-full" />
          </div>
        </ContextMenuTrigger>
        <ContextMenuContent>
          <div className="flex items-center gap-2 p-2 border-b border-gray-200">
            <span className="text-sm font-semibold text-gray-700">
              {permission.beschreibung}
            </span>
          </div>
          <CustomContextMenuItem
            label="Details anzeigen"
            onClick={() => setShowDetailsDialog(true)}
            icon={<EyeIcon />}
          />
          <CustomContextMenuItem
            label="Nutzer anzeigen"
            onClick={() => setShowUserDialog(true)}
            icon={<UsersIcon />}
          />
        </ContextMenuContent>
      </ContextMenu>
      {showDetailsDialog && (
        <DetailsDialog
          open={showDetailsDialog}
          onOpenChange={setShowDetailsDialog}
          permission={permission}
        />
      )}
      {showUserDialog && (
        <RechteUserDialog
          open={showUserDialog}
          onOpenChange={setShowUserDialog}
          permission={permission}
        />
      )}
    </>
  )
}
