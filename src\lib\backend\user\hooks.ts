import {
  useMutation,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from '@tanstack/react-query'
import type { CREATE_USER_REQUEST_DTO } from './DTOs'
import { createUser } from './functions'
import {
  userGroupsRechteQueryOptions,
  userQueryOptions,
  userRechteQueryOptions,
  usersQueryOptions,
} from './queries'

export const useUsers = () => {
  return useSuspenseQuery(usersQueryOptions)
}

export const useUser = (userRef: number) => {
  return useSuspenseQuery(userQueryOptions(userRef))
}

export const useUserRechte = (userRef: number) => {
  return useSuspenseQuery(userRechteQueryOptions(userRef))
}

export const useUserRechteOptional = (userRef: number | null) => {
  return useQuery({
    ...userRechteQueryOptions(userRef ?? -1),
    enabled: userRef !== null && userRef > 0,
  })
}

export const useUserGroupsRechteOptional = (userRef: number | null) => {
  return useQuery({
    ...userGroupsRechteQueryOptions(userRef ?? -1),
    enabled: userRef !== null && userRef > 0,
  })
}

export const useCreateUser = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (newUser: CREATE_USER_REQUEST_DTO) => createUser(newUser),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
  })
}
