import {  motion} from 'framer-motion';
import type { DotProps } from './dots.types';

const dotVariants = {
    visible: (i: number) => ({
      opacity: 1,
      transition: {
        delay: i * 0.5,
        duration: 0.5,
      },
    }),
    hidden: { opacity: 0 },
  };

  export const Dot: React.FC<DotProps> = ({ children, custom, onCompleted, controls }) => (
    <motion.g
      variants={dotVariants}
      initial="hidden"
      animate={controls}
      exit="hidden"
      custom={custom}
      onAnimationComplete={onCompleted}
    >
      {children}
    </motion.g>
  );
