import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { UserPlus, Users } from 'lucide-react'

type AddDialogSelectProps = {
  onSelectUser: () => void
  onSelectGroup: () => void
}

export const AddDialogSelect = ({
  onSelectUser,
  onSelectGroup,
}: AddDialogSelectProps) => {
  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold text-foreground tracking-tight">
          Was möchtest Du erstellen?
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
        <Card className="group cursor-pointer border-2 border-border hover:border-primary/50 transition-all duration-200 hover:shadow-lg">
          <CardContent className="p-8">
            <Button
              variant="ghost"
              className="w-full h-auto p-0 hover:bg-transparent group-hover:scale-105 transition-transform duration-200"
              onClick={onSelectUser}
            >
              <div className="text-center space-y-4">
                <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50 flex items-center justify-center ring-1 ring-blue-200/50 dark:ring-blue-800/50 mx-auto group-hover:scale-110 transition-transform duration-200">
                  <UserPlus className="w-10 h-10 text-blue-600 dark:text-blue-400" />
                </div>

                <div className="space-y-2">
                  <h3 className="text-xl font-semibold text-foreground">
                    Neuen Benutzer
                  </h3>
                </div>

                <div className="pt-2">
                  <div className="inline-flex items-center gap-2 text-sm text-primary font-medium">
                    Benutzer erstellen
                    <UserPlus className="w-4 h-4" />
                  </div>
                </div>
              </div>
            </Button>
          </CardContent>
        </Card>

        <Card className="group cursor-pointer border-2 border-border hover:border-primary/50 transition-all duration-200 hover:shadow-lg">
          <CardContent className="p-8">
            <Button
              variant="ghost"
              className="w-full h-auto p-0 hover:bg-transparent group-hover:scale-105 transition-transform duration-200"
              onClick={onSelectGroup}
            >
              <div className="text-center space-y-4">
                <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950/50 dark:to-emerald-900/50 flex items-center justify-center ring-1 ring-emerald-200/50 dark:ring-emerald-800/50 mx-auto group-hover:scale-110 transition-transform duration-200">
                  <Users className="w-10 h-10 text-emerald-600 dark:text-emerald-400" />
                </div>

                <div className="space-y-2">
                  <h3 className="text-xl font-semibold text-foreground">
                    Neue Gruppe
                  </h3>
                </div>

                <div className="pt-2">
                  <div className="inline-flex items-center gap-2 text-sm text-primary font-medium">
                    Gruppe erstellen
                    <Users className="w-4 h-4" />
                  </div>
                </div>
              </div>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
