import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import type {
  USER_RECHTE_RESPONSE_DTO,
  UserItem,
} from '@/lib/backend/user/DTOs'
import { useUserRechteOptional } from '@/lib/backend/user/hooks'
import { ArrowLeft, ArrowRight, Search } from 'lucide-react'
import { useMemo, useState } from 'react'
import { UserDialogUserCompareEmpty } from './user-dialog-user-compare-empty'

interface UserDialogUserCompareProps {
  user: UserItem
  selectedCompareUser?: UserItem | null
  setSelectedCompareUser: (user: UserItem | null) => void
}

export const UserDialogUserCompare: React.FC<UserDialogUserCompareProps> = ({
  user,
  selectedCompareUser,
  setSelectedCompareUser,
}) => {
  const { data: activeUserRechte } = useUserRechteOptional(user.ref!)
  const { data: compareUserRechte } = useUserRechteOptional(
    selectedCompareUser?.ref!,
  )

  const [searchTerm, setSearchTerm] = useState('')
  const [selectedActiveRechte, setSelectedActiveRechte] = useState<Set<number>>(
    new Set(),
  )
  const [selectedCompareRechte, setSelectedCompareRechte] = useState<
    Set<number>
  >(new Set())

  const activeRechteRefs = new Set(activeUserRechte?.map((r) => r.refAco))
  const compareRechteRefs = new Set(compareUserRechte?.map((r) => r.refAco))

  const onlyActiveHas = useMemo(
    () =>
      activeUserRechte?.filter((r) => !compareRechteRefs.has(r.refAco)) || [],
    [activeUserRechte, compareRechteRefs],
  )

  const onlyCompareHas = useMemo(
    () =>
      compareUserRechte?.filter((r) => !activeRechteRefs.has(r.refAco)) || [],
    [compareUserRechte, activeRechteRefs],
  )

  const filteredActiveRechte = useMemo(
    () =>
      onlyActiveHas.filter((rechte) => {
        const name = rechte.name?.toLowerCase() || ''
        const refAco = rechte.refAco?.toString().toLowerCase() || ''
        const searchLower = searchTerm.toLowerCase()
        return name.includes(searchLower) || refAco.includes(searchLower)
      }),
    [onlyActiveHas, searchTerm],
  )

  const filteredCompareRechte = useMemo(
    () =>
      onlyCompareHas.filter((rechte) => {
        const name = rechte.name?.toLowerCase() || ''
        const refAco = rechte.refAco?.toString().toLowerCase() || ''
        const searchLower = searchTerm.toLowerCase()
        return name.includes(searchLower) || refAco.includes(searchLower)
      }),
    [onlyCompareHas, searchTerm],
  )

  const formatRechteInfo = (rechte: USER_RECHTE_RESPONSE_DTO[number]) => {
    return `${rechte.name || 'Unbekannt'} (${rechte.rechteFlags || 'N/A'})`
  }

  const assignRechteToUser = (
    rechte: USER_RECHTE_RESPONSE_DTO[number],
    targetUser: UserItem,
  ) => {
    console.log(`Recht ${rechte.refAco} wird ${targetUser.userName} zugewiesen`)
  }

  const assignSelectedRechteToCompareUser = () => {
    const rechteToAssign = onlyActiveHas.filter(
      (r) => r.refAco && selectedActiveRechte.has(r.refAco),
    )
    rechteToAssign.forEach((rechte) => {
      if (selectedCompareUser) {
        assignRechteToUser(rechte, selectedCompareUser)
      }
    })
    setSelectedActiveRechte(new Set())
  }

  const assignSelectedRechteToActiveUser = () => {
    const rechteToAssign = onlyCompareHas.filter(
      (r) => r.refAco && selectedCompareRechte.has(r.refAco),
    )
    rechteToAssign.forEach((rechte) => {
      assignRechteToUser(rechte, user)
    })
    setSelectedCompareRechte(new Set())
  }

  const toggleActiveRecht = (refAco: number) => {
    const newSelected = new Set(selectedActiveRechte)
    if (newSelected.has(refAco)) {
      newSelected.delete(refAco)
    } else {
      newSelected.add(refAco)
    }
    setSelectedActiveRechte(newSelected)
  }

  const toggleCompareRecht = (refAco: number) => {
    const newSelected = new Set(selectedCompareRechte)
    if (newSelected.has(refAco)) {
      newSelected.delete(refAco)
    } else {
      newSelected.add(refAco)
    }
    setSelectedCompareRechte(newSelected)
  }

  const selectAllActiveRechte = () => {
    setSelectedActiveRechte(
      new Set(
        filteredActiveRechte
          .map((r) => r.refAco)
          .filter((refAco): refAco is number => refAco !== undefined),
      ),
    )
  }

  const selectAllCompareRechte = () => {
    setSelectedCompareRechte(
      new Set(
        filteredCompareRechte
          .map((r) => r.refAco)
          .filter((refAco): refAco is number => refAco !== undefined),
      ),
    )
  }

  const clearActiveSelection = () => {
    setSelectedActiveRechte(new Set())
  }

  const clearCompareSelection = () => {
    setSelectedCompareRechte(new Set())
  }

  if (!selectedCompareUser) {
    return <UserDialogUserCompareEmpty />
  }

  if (!activeUserRechte || !compareUserRechte) {
    return (
      <div className="space-y-4">
        <Separator />
        <div className="text-center py-8">
          <div className="text-gray-500 text-sm">Lade Benutzerrechte...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4 h-full">
      <Separator />

      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Rechte durchsuchen..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 h-9"
        />
      </div>

      <div className="grid grid-cols-2 gap-6 h-[500px]">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Badge variant="default" className="text-xs font-medium">
              {user.userName}
            </Badge>
            <div className="text-xs text-gray-500">
              {filteredActiveRechte.length} Rechte
            </div>
          </div>

          {filteredActiveRechte.length > 0 ? (
            <>
              <div className="flex items-center gap-2 text-xs">
                <Checkbox
                  checked={
                    selectedActiveRechte.size === filteredActiveRechte.length
                  }
                  onCheckedChange={(checked) =>
                    checked ? selectAllActiveRechte() : clearActiveSelection()
                  }
                />
                <span className="text-gray-600">Alle auswählen</span>
                {selectedActiveRechte.size > 0 && (
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-6 px-2 ml-auto"
                    onClick={assignSelectedRechteToCompareUser}
                  >
                    <ArrowRight className="h-3 w-3 mr-1" />
                    {selectedActiveRechte.size} zuweisen
                  </Button>
                )}
              </div>

              <ScrollArea className="h-[420px] bg-blue-50/50 border rounded-lg p-3">
                <div className="space-y-2">
                  {filteredActiveRechte.map((rechte) => (
                    <div
                      key={rechte.refAco}
                      className={`flex items-start gap-3 p-2 rounded-md transition-colors hover:bg-white/80 ${
                        rechte.refAco && selectedActiveRechte.has(rechte.refAco)
                          ? 'bg-blue-100 border border-blue-200'
                          : 'bg-white/50'
                      }`}
                    >
                      <Checkbox
                        checked={
                          rechte.refAco
                            ? selectedActiveRechte.has(rechte.refAco)
                            : false
                        }
                        onCheckedChange={() =>
                          rechte.refAco && toggleActiveRecht(rechte.refAco)
                        }
                        className="mt-0.5"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="text-xs font-medium text-gray-900 break-words">
                          {rechte.name || 'Unbekannt'}
                        </div>
                        <div className="text-xs text-gray-500 mt-0.5">
                          {rechte.rechteFlags || 'N/A'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </>
          ) : (
            <div className="flex items-center justify-center h-[460px] bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
              <div className="text-center">
                <div className="text-sm text-gray-500">
                  Keine exklusiven Rechte
                </div>
                {searchTerm && (
                  <div className="text-xs text-gray-400 mt-1">
                    Keine Treffer für "{searchTerm}"
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Badge variant="secondary" className="text-xs font-medium">
              {selectedCompareUser.userName}
            </Badge>
            <div className="text-xs text-gray-500">
              {filteredCompareRechte.length} Rechte
            </div>
          </div>

          {filteredCompareRechte.length > 0 ? (
            <>
              <div className="flex items-center gap-2 text-xs">
                <Checkbox
                  checked={
                    selectedCompareRechte.size === filteredCompareRechte.length
                  }
                  onCheckedChange={(checked) =>
                    checked ? selectAllCompareRechte() : clearCompareSelection()
                  }
                />
                <span className="text-gray-600">Alle auswählen</span>
                {selectedCompareRechte.size > 0 && (
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-6 px-2 ml-auto"
                    onClick={assignSelectedRechteToActiveUser}
                  >
                    <ArrowLeft className="h-3 w-3 mr-1" />
                    {selectedCompareRechte.size} zuweisen
                  </Button>
                )}
              </div>

              <ScrollArea className="h-[420px] bg-green-50/50 border rounded-lg p-3">
                <div className="space-y-2">
                  {filteredCompareRechte.map((rechte) => (
                    <div
                      key={rechte.refAco}
                      className={`flex items-start gap-3 p-2 rounded-md transition-colors hover:bg-white/80 ${
                        rechte.refAco &&
                        selectedCompareRechte.has(rechte.refAco)
                          ? 'bg-green-100 border border-green-200'
                          : 'bg-white/50'
                      }`}
                    >
                      <Checkbox
                        checked={
                          rechte.refAco
                            ? selectedCompareRechte.has(rechte.refAco)
                            : false
                        }
                        onCheckedChange={() =>
                          rechte.refAco && toggleCompareRecht(rechte.refAco)
                        }
                        className="mt-0.5"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="text-xs font-medium text-gray-900 break-words">
                          {rechte.name || 'Unbekannt'}
                        </div>
                        <div className="text-xs text-gray-500 mt-0.5">
                          {rechte.rechteFlags || 'N/A'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </>
          ) : (
            <div className="flex items-center justify-center h-[460px] bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
              <div className="text-center">
                <div className="text-sm text-gray-500">
                  Keine exklusiven Rechte
                </div>
                {searchTerm && (
                  <div className="text-xs text-gray-400 mt-1">
                    Keine Treffer für "{searchTerm}"
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
