import type { components, paths } from 'schema'

export type GROUP_RESPONSE_DTO =
  paths['/api/groups']['get']['responses']['200']['content']['application/json']

export type GroupItem = components['schemas']['SysBenGrpView']

export type GROUP_USERS_RESPONSE_DTO =
  paths['/api/groups/{groupRef}/users']['get']['responses']['200']['content']['application/json']


export type GROUP_RECHTE_RESPONSE_DTO =
  paths['/api/groups/{groupRef}/acos']['get']['responses']['200']['content']['application/json']

  export type CREATE_GROUP_REQUEST_DTO =
    paths['/api/groups']['post']['requestBody']['content']['application/json']
