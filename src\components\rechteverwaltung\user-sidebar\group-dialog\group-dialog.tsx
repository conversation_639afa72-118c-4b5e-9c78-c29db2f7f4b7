import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import type { GROUP_RESPONSE_DTO } from '@/lib/backend/groups/DTOs'
import { UserDialogGroupContent } from '../user-dialog/user-dialog-group-content'
import { GroupDialogContentChangeUsers } from './group-dialog-content-change-users'

export type GroupDialogMode = 'details' | 'users'

interface GroupDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  mode: GroupDialogMode
  group: GROUP_RESPONSE_DTO[number] | null
}

export const GroupDialog = ({
  open,
  onOpenChange,
  mode,
  group,
}: GroupDialogProps) => {
  if (!group || !group.ref) return null

  const getDialogTitle = () => {
    switch (mode) {
      case 'details':
        return 'Gruppendetails'
      case 'users':
        return 'Benutzer in der Gruppe'
      default:
        return 'Gruppe'
    }
  }

  const renderContent = () => {
    switch (mode) {
      case 'details':
        return (
          <UserDialogGroupContent
            group={group}
            onClose={() => onOpenChange(false)}
          />
        )
      case 'users':
        return <GroupDialogContentChangeUsers group={group} />
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="min-w-4xl">
        <DialogHeader>
          <DialogTitle>{getDialogTitle()}</DialogTitle>
        </DialogHeader>
        {renderContent()}
      </DialogContent>
    </Dialog>
  )
}
