import { $api } from '../client'

export const rechteQueryOptions = $api.queryOptions('get', '/api/acos', {
  queryKey: ['rechte'],
})

export const rechteuserQueryOptions = (ACO_REF: number) =>
  $api.queryOptions('get', '/api/acos/{acoRef}/users', {
    queryKey: ['rechte-user', ACO_REF],
    params: {
      path: {
        acoRef: ACO_REF,
      },
    },
  })

export const rechtegroupQueryOptions = (ACO_REF: number) =>
  $api.queryOptions('get', '/api/acos/{acoRef}/groups', {
    queryKey: ['rechte-groups', ACO_REF],
    params: {
      path: {
        acoRef: ACO_REF,
      },
    },
  })
