import type { GROUP_RECHTE_RESPONSE_DTO } from '@/lib/backend/groups/DTOs'
import { useGroupRechte } from '@/lib/backend/groups/hooks'
import type { USER_RECHTE_RESPONSE_DTO } from '@/lib/backend/user/DTOs'
import {
  useUserGroupsRechteOptional,
  useUserRechteOptional,
} from '@/lib/backend/user/hooks'
import { useActiveUser } from '@/lib/stores/useActiveUser'

export type PermissionData =
  | USER_RECHTE_RESPONSE_DTO
  | GROUP_RECHTE_RESPONSE_DTO
  | null

export const useData = () => {
  const { activeUserRef, activeGroupRef } = useActiveUser()

  const userRechteQuery = useUserRechteOptional(activeUserRef)
  const userRechte = userRechteQuery?.data || []
  const userGroupRechteQuery = useUserGroupsRechteOptional(activeUserRef)
  const userGroupRechte = userGroupRechteQuery?.data || []

  const groupRechteQuery = useGroupRechte(activeGroupRef)
  const groupRechte = groupRechteQuery?.data || []

  const primaryPermissions: PermissionData = activeGroupRef
    ? groupRechte
    : userRechte

  return { userGroupRechte, primaryPermissions }
}
