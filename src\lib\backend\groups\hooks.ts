import {
  useMutation,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from '@tanstack/react-query'
import type { CREATE_GROUP_REQUEST_DTO } from './DTOs'
import { createGroup } from './functions'
import {
  groupACOsQueryOptions,
  groupsQueryOptions,
  groupUsersQueryOptions,
  userqroupsQueryOptions,
} from './queries'

export const useGroups = () => {
  return useSuspenseQuery(groupsQueryOptions)
}

export const useUserGroups = (userRef: number) => {
  return useSuspenseQuery(userqroupsQueryOptions(userRef))
}

export const useGroupUsers = (groupREF: number) => {
  return useQuery(groupUsersQueryOptions(groupREF))
}

export const useGroupRechte = (groupREF: number | null) => {
  return useQuery({
    ...groupACOsQueryOptions(groupREF || 0),
    enabled: !!groupREF,
  })
}

export const useCreateGroup = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (newGroup: CREATE_GROUP_REQUEST_DTO) => createGroup(newGroup),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['groups'] })
    },
  })
}
