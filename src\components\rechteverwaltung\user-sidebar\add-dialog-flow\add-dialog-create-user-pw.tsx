import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import type { CREATE_USER_REQUEST_DTO } from '@/lib/backend/user/DTOs'
import { useState } from 'react'
import { toast } from 'sonner'

type UserDialogCreateUserPWProps = {
  newUser: CREATE_USER_REQUEST_DTO
  setNewUser: React.Dispatch<React.SetStateAction<CREATE_USER_REQUEST_DTO>>
  handleCreateUserSubmit: () => void
  setIsOpen?: (isOpen: boolean) => void
}

export const UserDialogCreateUserPW = ({
  newUser,
  setNewUser,
  handleCreateUserSubmit,
  setIsOpen = () => {},
}: UserDialogCreateUserPWProps) => {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value)
    setNewUser({ ...newUser, password: e.target.value })
  }

  const handleConfirmPasswordChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    setConfirmPassword(e.target.value)
  }

  const handleSubmit = () => {
    if (password !== confirmPassword) {
      toast.error('Passwörter stimmen nicht überein')
      return
    }
    handleCreateUserSubmit()
    setPassword('')
    setConfirmPassword('')
  }

  return (
    <Dialog open={true} onOpenChange={() => {setIsOpen(false)}}>
      <DialogContent className="rounded-lg shadow-lg p-6 bg-white">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-gray-800">
            Passwort festlegen
          </DialogTitle>
          <DialogDescription className="text-sm text-gray-600">
            Bitte lege ein sicheres Passwort für den neuen Benutzer{' '}
            {newUser.name} fest.
          </DialogDescription>
        </DialogHeader>
        <div className="mt-6 gap-4 flex flex-col">
          <div>
            <Label
              htmlFor="password"
              className="text-sm font-medium text-gray-700"
            >
              Passwort
            </Label>
            <Input
              type="password"
              placeholder="Passwort eingeben"
              value={password}
              onChange={handlePasswordChange}
            />
          </div>
          <div>
            <Label
              htmlFor="confirmPassword"
              className="text-sm font-medium text-gray-700"
            >
              Passwort bestätigen
            </Label>
            <Input
              type="password"
              placeholder="Passwort bestätigen"
              value={confirmPassword}
              onChange={handleConfirmPasswordChange}
            />
          </div>
        </div>
        <div className="mt-6 flex justify-end">
          <Button onClick={handleSubmit}>Speichern</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
