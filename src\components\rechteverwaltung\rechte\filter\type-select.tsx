import { Combobox } from '@/components/ui/combobox'
import { useTypes } from '@/lib/backend/typen/hooks'
import { useFilterStore } from '@/lib/stores/useFilterStore'

export const TypeSelect = () => {
  const { data: types } = useTypes()
  const { selectedTypes, setSelectedTypes } = useFilterStore()

  const handleSelect = (value: string | string[]) => {
    if (Array.isArray(value)) {
      setSelectedTypes(value)
    } else {
      setSelectedTypes([value])
    }
  }

  const items =
    types?.map((type) => ({
      value: type,
      label: type,
    })) || []

  return (
    <Combobox
      items={items}
      placeholder="<PERSON><PERSON>hle einen Ty<PERSON>"
      multipleSelection
      value={selectedTypes}
      setValue={handleSelect}
    />
  )
}
