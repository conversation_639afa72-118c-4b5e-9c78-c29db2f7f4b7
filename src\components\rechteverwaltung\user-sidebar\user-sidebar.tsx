import { Button } from '@/components/ui/button'
import type { GROUP_RESPONSE_DTO } from '@/lib/backend/groups/DTOs'
import type { USER_RESPONSE_DTO, UserItem } from '@/lib/backend/user/DTOs'
import { UserPlus, Users } from 'lucide-react'
import { useMemo, useState } from 'react'
import { AddDialogFlow } from './add-dialog-flow/add-dialog-flow'
import { GroupDialog, type GroupDialogMode } from './group-dialog/group-dialog'
import { UserDialog, type UserDialogMode } from './user-dialog/user-dialog'
import { UserSidebarFilter } from './user-sidebar-filter'
import { UserSidebarList } from './user-sidebar-list/user-sidebar-list'

type FilterType = 'all' | 'users' | 'groups'

export const UserSidebar = ({
  users,
  groups,
}: {
  users: USER_RESPONSE_DTO
  groups: GROUP_RESPONSE_DTO
}) => {
  const [userDialogOpen, setUserDialogOpen] = useState(false)
  const [userDialogMode, setUserDialogMode] =
    useState<UserDialogMode>('details')
  const [selectedUser, setSelectedUser] = useState<UserItem | null>(null)

  const [groupDialogOpen, setGroupDialogOpen] = useState(false)
  const [groupDialogMode, setGroupDialogMode] =
    useState<GroupDialogMode>('details')
  const [selectedGroup, setSelectedGroup] = useState<
    GROUP_RESPONSE_DTO[number] | null
  >(null)

  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<FilterType>('all')
  const [showActiveOnly, setShowActiveOnly] = useState(false)

  const filteredData = useMemo(() => {
    const filteredUsers = users?.filter((user) => {
      if (!user) return false

      const matchesSearch =
        (user.userName?.toLowerCase().includes(searchTerm.toLowerCase()) ??
          false) ||
        (user.userId?.toLowerCase().includes(searchTerm.toLowerCase()) ??
          false) ||
        (user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false)

      const isActive = showActiveOnly
        ? user.status?.toUpperCase() === 'AKT'
        : true

      return matchesSearch && isActive
    }) ?? []

    const filteredGroups = groups.filter((group) => {
      const matchesSearch =
        (group.groupName?.toLowerCase().includes(searchTerm.toLowerCase()) ??
          false) ||
        (group.groupId?.toLowerCase().includes(searchTerm.toLowerCase()) ??
          false)

      return matchesSearch
    })

    return { filteredUsers, filteredGroups }
  }, [users, groups, searchTerm, showActiveOnly])

  const handleOpenUserDialog = (user: UserItem, mode: UserDialogMode) => {
    setSelectedUser(user)
    setUserDialogMode(mode)
    setUserDialogOpen(true)
  }

  const handleOpenGroupDialog = (
    group: GROUP_RESPONSE_DTO[number],
    mode: GroupDialogMode,
  ) => {
    setSelectedGroup(group)
    setGroupDialogMode(mode)
    setGroupDialogOpen(true)
  }

  const displayUsers = filterType === 'groups' ? [] : filteredData.filteredUsers
  const displayGroups =
    filterType === 'users' ? [] : filteredData.filteredGroups
  const totalDisplayed = displayUsers.length + displayGroups.length

  if (!users || !groups) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-muted-foreground">Lade Benutzer und Gruppen...</p>
      </div>
    )
  }

  const activeUsersCount = users.filter(
    (user) => user.status?.toUpperCase() === 'AKT',
  ).length

  return (
    <div className="h-full flex flex-col border rounded-2xl p-4">
      <div className="flex items-center justify-between gap-3 mb-4">
        <div className="flex items-center gap-2">
          <div className={`p-2 rounded-lg bg-primary-gradient`}>
            <Users className="h-4 w-4 text-black" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-800">Benutzer & Gruppen</h3>
            <p className="text-xs text-gray-500">
              {totalDisplayed} von {users.length + groups.length} •{' '}
              {activeUsersCount} aktiv
            </p>
          </div>
        </div>
        <AddDialogFlow
          trigger={
            <Button>
              <UserPlus />
            </Button>
          }
        />
      </div>

      <UserSidebarFilter
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        filterType={filterType}
        onFilterTypeChange={setFilterType}
        showActiveOnly={showActiveOnly}
        onShowActiveOnlyChange={setShowActiveOnly}
      />

      <UserSidebarList
        users={displayUsers}
        groups={displayGroups}
        onOpenUserDialog={handleOpenUserDialog}
        onOpenGroupDialog={handleOpenGroupDialog}
      />

      <UserDialog
        open={userDialogOpen}
        onOpenChange={setUserDialogOpen}
        mode={userDialogMode}
        user={selectedUser}
      />

      <GroupDialog
        open={groupDialogOpen}
        onOpenChange={setGroupDialogOpen}
        mode={groupDialogMode}
        group={selectedGroup}
      />
    </div>
  )
}
